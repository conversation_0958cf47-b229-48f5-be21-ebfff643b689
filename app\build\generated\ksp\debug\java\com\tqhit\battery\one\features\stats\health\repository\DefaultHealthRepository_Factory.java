package com.tqhit.battery.one.features.stats.health.repository;

import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider;
import com.tqhit.battery.one.features.stats.health.cache.HealthCache;
import com.tqhit.battery.one.manager.charge.ChargingSessionManager;
import com.tqhit.battery.one.repository.AppRepository;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DefaultHealthRepository_Factory implements Factory<DefaultHealthRepository> {
  private final Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider;

  private final Provider<ChargingSessionManager> chargingSessionManagerProvider;

  private final Provider<AppRepository> appRepositoryProvider;

  private final Provider<HealthCache> healthCacheProvider;

  private final Provider<HistoryBatteryRepository> historyBatteryRepositoryProvider;

  public DefaultHealthRepository_Factory(
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider,
      Provider<ChargingSessionManager> chargingSessionManagerProvider,
      Provider<AppRepository> appRepositoryProvider, Provider<HealthCache> healthCacheProvider,
      Provider<HistoryBatteryRepository> historyBatteryRepositoryProvider) {
    this.coreBatteryStatsProvider = coreBatteryStatsProvider;
    this.chargingSessionManagerProvider = chargingSessionManagerProvider;
    this.appRepositoryProvider = appRepositoryProvider;
    this.healthCacheProvider = healthCacheProvider;
    this.historyBatteryRepositoryProvider = historyBatteryRepositoryProvider;
  }

  @Override
  public DefaultHealthRepository get() {
    return newInstance(coreBatteryStatsProvider.get(), chargingSessionManagerProvider.get(), appRepositoryProvider.get(), healthCacheProvider.get(), historyBatteryRepositoryProvider.get());
  }

  public static DefaultHealthRepository_Factory create(
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider,
      Provider<ChargingSessionManager> chargingSessionManagerProvider,
      Provider<AppRepository> appRepositoryProvider, Provider<HealthCache> healthCacheProvider,
      Provider<HistoryBatteryRepository> historyBatteryRepositoryProvider) {
    return new DefaultHealthRepository_Factory(coreBatteryStatsProvider, chargingSessionManagerProvider, appRepositoryProvider, healthCacheProvider, historyBatteryRepositoryProvider);
  }

  public static DefaultHealthRepository newInstance(
      CoreBatteryStatsProvider coreBatteryStatsProvider,
      ChargingSessionManager chargingSessionManager, AppRepository appRepository,
      HealthCache healthCache, HistoryBatteryRepository historyBatteryRepository) {
    return new DefaultHealthRepository(coreBatteryStatsProvider, chargingSessionManager, appRepository, healthCache, historyBatteryRepository);
  }
}
