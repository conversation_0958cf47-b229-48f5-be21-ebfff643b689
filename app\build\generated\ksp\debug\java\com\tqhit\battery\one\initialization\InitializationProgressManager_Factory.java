package com.tqhit.battery.one.initialization;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class InitializationProgressManager_Factory implements Factory<InitializationProgressManager> {
  private final Provider<Context> contextProvider;

  private final Provider<ServiceInitializationHelper> serviceInitializationHelperProvider;

  public InitializationProgressManager_Factory(Provider<Context> contextProvider,
      Provider<ServiceInitializationHelper> serviceInitializationHelperProvider) {
    this.contextProvider = contextProvider;
    this.serviceInitializationHelperProvider = serviceInitializationHelperProvider;
  }

  @Override
  public InitializationProgressManager get() {
    return newInstance(contextProvider.get(), serviceInitializationHelperProvider.get());
  }

  public static InitializationProgressManager_Factory create(Provider<Context> contextProvider,
      Provider<ServiceInitializationHelper> serviceInitializationHelperProvider) {
    return new InitializationProgressManager_Factory(contextProvider, serviceInitializationHelperProvider);
  }

  public static InitializationProgressManager newInstance(Context context,
      ServiceInitializationHelper serviceInitializationHelper) {
    return new InitializationProgressManager(context, serviceInitializationHelper);
  }
}
