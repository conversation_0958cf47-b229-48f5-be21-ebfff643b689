<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_splash" modulePackage="com.tqhit.battery.one" filePath="app\src\main\res\layout\activity_splash.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_splash_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="82" endOffset="51"/></Target><Target id="@+id/ivLogo" view="ImageView"><Expressions/><location startLine="7" startOffset="4" endLine="17" endOffset="59"/></Target><Target id="@+id/tvAppName" view="TextView"><Expressions/><location startLine="19" startOffset="4" endLine="31" endOffset="59"/></Target><Target id="@+id/progressContainer" view="LinearLayout"><Expressions/><location startLine="34" startOffset="4" endLine="80" endOffset="18"/></Target><Target id="@+id/tvInitializationStatus" view="TextView"><Expressions/><location startLine="50" startOffset="8" endLine="58" endOffset="38"/></Target><Target id="@+id/progressBarInitialization" view="ProgressBar"><Expressions/><location startLine="61" startOffset="8" endLine="68" endOffset="34"/></Target><Target id="@+id/tvProgressPercentage" view="TextView"><Expressions/><location startLine="71" startOffset="8" endLine="78" endOffset="33"/></Target></Targets></Layout>