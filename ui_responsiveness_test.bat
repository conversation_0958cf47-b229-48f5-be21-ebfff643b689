@echo off
REM UI Responsiveness Testing for Enhanced Splash Activity
REM Measures progress bar update latency, animation smoothness, and touch responsiveness

echo ========================================
echo UI Responsiveness Testing
echo Target: <50ms progress updates, smooth animations
echo ========================================

set ADB_PATH=E:\IDE\Android\SDK\platform-tools\adb.exe
set APP_ID=com.fc.p.tj.charginganimation.batterycharging.chargeeffect
set TIMESTAMP=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%

REM Create results directory
if not exist ui_test_results mkdir ui_test_results

echo Starting UI Responsiveness Test at %TIMESTAMP%
echo.

REM Test 1: Progress Bar Update Latency
echo === Test 1: Progress Bar Update Latency ===
echo Measuring time between progress state changes and UI updates...

%ADB_PATH% shell am force-stop %APP_ID%
%ADB_PATH% shell pm clear %APP_ID%
timeout /t 2 /nobreak >nul

REM Start detailed UI monitoring
start /b %ADB_PATH% logcat -s SPLASH_PROGRESS:V InitProgressManager:V SplashActivity:V > ui_test_results\progress_latency_%TIMESTAMP%.log

echo Launching app for progress latency test...
%ADB_PATH% shell am start -n %APP_ID%/.activity.splash.SplashActivity

echo Monitoring progress updates for 8 seconds...
timeout /t 8 /nobreak >nul

taskkill /f /im adb.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo Progress latency test completed.
echo.

REM Test 2: Animation Smoothness
echo === Test 2: Animation Smoothness Testing ===
echo Testing for frame drops and stuttering during progress animations...

%ADB_PATH% shell am force-stop %APP_ID%
timeout /t 2 /nobreak >nul

REM Enable GPU rendering profile
%ADB_PATH% shell setprop debug.hwui.profile visual_bars

start /b %ADB_PATH% logcat -s hwui:V Choreographer:V > ui_test_results\animation_smoothness_%TIMESTAMP%.log

echo Launching app for animation smoothness test...
%ADB_PATH% shell am start -n %APP_ID%/.activity.splash.SplashActivity

echo Monitoring animation performance for 10 seconds...
timeout /t 10 /nobreak >nul

REM Disable GPU profiling
%ADB_PATH% shell setprop debug.hwui.profile false

taskkill /f /im adb.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo Animation smoothness test completed.
echo.

REM Test 3: Touch Responsiveness During Initialization
echo === Test 3: Touch Responsiveness Testing ===
echo Testing touch response during background initialization...

%ADB_PATH% shell am force-stop %APP_ID%
timeout /t 2 /nobreak >nul

start /b %ADB_PATH% logcat -s Input:V TouchEvent:V SplashActivity:V > ui_test_results\touch_responsiveness_%TIMESTAMP%.log

echo Launching app for touch responsiveness test...
%ADB_PATH% shell am start -n %APP_ID%/.activity.splash.SplashActivity

echo Simulating touch events during initialization...
timeout /t 2 /nobreak >nul

REM Simulate touch events on splash screen
%ADB_PATH% shell input tap 540 960
timeout /t 1 /nobreak >nul
%ADB_PATH% shell input tap 540 960
timeout /t 1 /nobreak >nul
%ADB_PATH% shell input tap 540 960

echo Continuing to monitor for 5 more seconds...
timeout /t 5 /nobreak >nul

taskkill /f /im adb.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo Touch responsiveness test completed.
echo.

REM Test 4: Memory Usage During UI Updates
echo === Test 4: Memory Usage Analysis ===
echo Monitoring memory consumption during splash UI updates...

%ADB_PATH% shell am force-stop %APP_ID%
timeout /t 2 /nobreak >nul

REM Start memory monitoring
start /b %ADB_PATH% shell dumpsys meminfo %APP_ID% > ui_test_results\memory_baseline_%TIMESTAMP%.txt

echo Launching app for memory usage test...
%ADB_PATH% shell am start -n %APP_ID%/.activity.splash.SplashActivity

echo Monitoring memory usage during initialization...
timeout /t 3 /nobreak >nul
%ADB_PATH% shell dumpsys meminfo %APP_ID% > ui_test_results\memory_during_init_%TIMESTAMP%.txt

timeout /t 3 /nobreak >nul
%ADB_PATH% shell dumpsys meminfo %APP_ID% > ui_test_results\memory_post_init_%TIMESTAMP%.txt

echo Memory usage test completed.
echo.

REM Analyze Results
echo === Analyzing UI Responsiveness Results ===

echo Analyzing progress update latency...
if exist ui_test_results\progress_latency_%TIMESTAMP%.log (
    echo Progress Update Analysis: > ui_test_results\ui_analysis_%TIMESTAMP%.txt
    findstr /c:"SPLASH_PROGRESS: UI updated" ui_test_results\progress_latency_%TIMESTAMP%.log >> ui_test_results\ui_analysis_%TIMESTAMP%.txt
    echo. >> ui_test_results\ui_analysis_%TIMESTAMP%.txt
    
    echo State Change Timing: >> ui_test_results\ui_analysis_%TIMESTAMP%.txt
    findstr /c:"SPLASH_PROGRESS: State update" ui_test_results\progress_latency_%TIMESTAMP%.log >> ui_test_results\ui_analysis_%TIMESTAMP%.txt
    echo. >> ui_test_results\ui_analysis_%TIMESTAMP%.txt
)

echo Analyzing animation performance...
if exist ui_test_results\animation_smoothness_%TIMESTAMP%.log (
    echo Animation Performance Analysis: >> ui_test_results\ui_analysis_%TIMESTAMP%.txt
    findstr /c:"Choreographer" ui_test_results\animation_smoothness_%TIMESTAMP%.log | findstr /c:"skipped" >> ui_test_results\ui_analysis_%TIMESTAMP%.txt
    echo. >> ui_test_results\ui_analysis_%TIMESTAMP%.txt
)

echo Analyzing touch responsiveness...
if exist ui_test_results\touch_responsiveness_%TIMESTAMP%.log (
    echo Touch Response Analysis: >> ui_test_results\ui_analysis_%TIMESTAMP%.txt
    findstr /c:"Input" ui_test_results\touch_responsiveness_%TIMESTAMP%.log >> ui_test_results\ui_analysis_%TIMESTAMP%.txt
    echo. >> ui_test_results\ui_analysis_%TIMESTAMP%.txt
)

echo Analyzing memory usage...
if exist ui_test_results\memory_during_init_%TIMESTAMP%.txt (
    echo Memory Usage Analysis: >> ui_test_results\ui_analysis_%TIMESTAMP%.txt
    echo Baseline Memory: >> ui_test_results\ui_analysis_%TIMESTAMP%.txt
    findstr /c:"TOTAL" ui_test_results\memory_baseline_%TIMESTAMP%.txt >> ui_test_results\ui_analysis_%TIMESTAMP%.txt
    echo During Initialization: >> ui_test_results\ui_analysis_%TIMESTAMP%.txt
    findstr /c:"TOTAL" ui_test_results\memory_during_init_%TIMESTAMP%.txt >> ui_test_results\ui_analysis_%TIMESTAMP%.txt
    echo Post Initialization: >> ui_test_results\ui_analysis_%TIMESTAMP%.txt
    findstr /c:"TOTAL" ui_test_results\memory_post_init_%TIMESTAMP%.txt >> ui_test_results\ui_analysis_%TIMESTAMP%.txt
    echo. >> ui_test_results\ui_analysis_%TIMESTAMP%.txt
)

echo ========================================
echo UI Responsiveness Test Results Summary
echo ========================================

echo Test completed at %date% %time%
echo Results saved in ui_test_results directory:
echo - progress_latency_%TIMESTAMP%.log
echo - animation_smoothness_%TIMESTAMP%.log  
echo - touch_responsiveness_%TIMESTAMP%.log
echo - memory_*_%TIMESTAMP%.txt
echo - ui_analysis_%TIMESTAMP%.txt

echo.
echo Key Metrics to Check:
echo 1. Progress update latency should be <50ms
echo 2. No "skipped frames" in Choreographer logs
echo 3. Touch events should be processed without delay
echo 4. Memory usage should remain stable

if exist ui_test_results\ui_analysis_%TIMESTAMP%.txt (
    echo.
    echo Quick Analysis Results:
    type ui_test_results\ui_analysis_%TIMESTAMP%.txt
)

pause
