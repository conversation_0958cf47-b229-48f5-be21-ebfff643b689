@echo off
REM Device Connection Test for TJ_BatteryOne Testing

echo ========================================
echo Device Connection Test
echo ========================================

set ADB_PATH=E:\IDE\Android\SDK\platform-tools\adb.exe
set APP_ID=com.fc.p.tj.charginganimation.batterycharging.chargeeffect

echo Testing ADB connection...
echo ADB Path: %ADB_PATH%
echo Application ID: %APP_ID%
echo.

REM Check if ADB exists
if not exist "%ADB_PATH%" (
    echo ERROR: ADB not found at %ADB_PATH%
    echo Please check the ADB path and try again.
    pause
    exit /b 1
)

echo ADB executable found.
echo.

REM Check ADB devices
echo Checking connected devices...
%ADB_PATH% devices

echo.
echo Checking for emulator-5554...
%ADB_PATH% devices | findstr "emulator-5554" >nul
if errorlevel 1 (
    echo WARNING: emulator-5554 not found.
    echo Please start the Android emulator and try again.
    echo.
    echo To start emulator:
    echo 1. Open Android Studio
    echo 2. Go to Tools ^> AVD Manager
    echo 3. Start an emulator
    echo.
    pause
    exit /b 1
) else (
    echo emulator-5554 found and connected.
)

echo.
echo Testing app installation...
%ADB_PATH% shell pm list packages | findstr "%APP_ID%" >nul
if errorlevel 1 (
    echo WARNING: App %APP_ID% not installed on device.
    echo Please build and install the app first.
    echo.
    echo To install app:
    echo 1. Build the project: gradlew assembleDebug
    echo 2. Install: adb install app\build\outputs\apk\debug\app-debug.apk
    echo.
    pause
    exit /b 1
) else (
    echo App %APP_ID% is installed on device.
)

echo.
echo Testing activity launch...
%ADB_PATH% shell am start -n %APP_ID%/com.tqhit.battery.one.activity.splash.SplashActivity
if errorlevel 1 (
    echo ERROR: Failed to launch SplashActivity
    echo Please check the activity name and try again.
    pause
    exit /b 1
) else (
    echo SplashActivity launched successfully.
)

echo.
echo ========================================
echo Device Connection Test PASSED
echo ========================================
echo.
echo The device is ready for testing.
echo You can now run the validation tests.

pause
