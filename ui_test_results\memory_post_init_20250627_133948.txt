Applications Memory Usage (in Kilobytes):
Uptime: 5282373 Realtime: 5282373

** MEMINFO in pid 9247 [com.fc.p.tj.charginganimation.batterycharging.chargeeffect] **
                   Pss  Private  Private  SwapPss      Rss     Heap     Heap     Heap
                 Total    Dirty    Clean    Dirty    Total     Size    Alloc     Free
                ------   ------   ------   ------   ------   ------   ------   ------
  Native Heap    77243    77224        4       31    78168    93380    85364     3694
  Dalvik Heap    47614    42600     4976       19    48708    48207    24104    24103
 Dalvik Other    35373    27184       48        0    43808                           
        Stack    12424    12424        0        0    12428                           
       Ashmem      329      176        0        0     1208                           
    Other dev      149        8      140        0      464                           
     .so mmap    13135     1048     4468       14    50144                           
    .jar mmap     6325        0     3068        0    51724                           
    .apk mmap    32141      600     6204        0   107792                           
    .ttf mmap      313        0       60        0     1280                           
    .dex mmap      843        0      100        0     4184                           
    .oat mmap       46        0       12        0     2240                           
    .art mmap    13331     9284     3524       51    28016                           
   Other mmap     6130      392      336        0    14676                           
      Unknown   120248   120236        8        0   120684                           
        TOTAL   365759   291176    22948      115   565524   141587   109468    27797
 
 App Summary
                       Pss(KB)                        Rss(KB)
                        ------                         ------
           Java Heap:    55408                          76724
         Native Heap:    77224                          78168
                Code:    16308                         234392
               Stack:    12424                          12428
            Graphics:        0                              0
       Private Other:   152760
              System:    51635
             Unknown:                                  163812
 
           TOTAL PSS:   365759            TOTAL RSS:   565524       TOTAL SWAP PSS:      115
 
 Objects
               Views:      213         ViewRootImpl:        1
         AppContexts:       27           Activities:        1
              Assets:       30        AssetManagers:        0
       Local Binders:      199        Proxy Binders:      114
       Parcel memory:      184         Parcel count:      419
    Death Recipients:       13             WebViews:        3
 
 Native Allocations
                         Count                       Total(kB)
                        ------                         ------
   Bitmap (malloced):       12                          37117
    Other (malloced):     1543                            146
 Other (nonmalloced):      344                            208
 
 SQL
         MEMORY_USED:     2259
  PAGECACHE_OVERFLOW:      979          MALLOC_SIZE:       46
 
 DATABASES
      pgsz     dbsz   Lookaside(b) cache hits cache misses cache size  Dbname
PER CONNECTION STATS
         4       32             85    23    65     8  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/track_manager_metrics_sdk.db
         4       52            123   208    66    19  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/com.im_10.8.3.db
         4       44             95    30    46     9  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/bigo_ads_sdk.db
         4       40            109    20    64    11  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/chartboost_exoplayer.db
         4       36            123    93   101    24  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/component_20799a27-fa80-4b36-b2db-0f8141f24180.db
         4       24             18    18    98     1  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/metrics-db
         4        8                    0     0     0    (attached) temp
         4       28             73     8    38     7  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/ads.db
         4       36            115   100   116    25  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/component_main.db
         4       16             27     1    29     4  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/track_manager_monitor.db
         4       20             42     1    34     5  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/auto_inapp.db
         4       96             45     2    65     5  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/mbridge.msdk.db
         4       20             24     0    32     3  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/npth_log.db
         4       36            123    51    81    22  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/component_322a737a-a0ca-44e0-bc85-649b1c7c1db6.db
         4      100             18    40   151     1  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/androidx.work.workdb
         4        8                    0     0     0    (attached) temp
         4      100             20     5    22     1  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/androidx.work.workdb (3)
         4       84            112    13    61    12  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/ttopensdk.db
         4       56             92    35    88    10  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/com.google.android.datatransport.events
         4       32             93   105   102    11  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/service_main.db
POOL STATS
     cache hits  cache misses    cache size  Dbname
             23            66            89  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/track_manager_metrics_sdk.db
            208            67           275  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/com.im_10.8.3.db
             30            47            77  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/bigo_ads_sdk.db
             15            70            85  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/chartboost_exoplayer.db
             93           102           195  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/component_20799a27-fa80-4b36-b2db-0f8141f24180.db
             19           130           149  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/metrics-db
              8            39            47  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/ads.db
            100           117           217  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/component_main.db
              1            30            31  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/track_manager_monitor.db
              1            35            36  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/auto_inapp.db
              2            66            68  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/mbridge.msdk.db
              0            33            33  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/npth_log.db
             51            82           133  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/component_322a737a-a0ca-44e0-bc85-649b1c7c1db6.db
             46           207           253  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/androidx.work.workdb
             13            62            75  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/ttopensdk.db
             35            89           124  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/com.google.android.datatransport.events
            105           103           208  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/service_main.db
