package com.tqhit.battery.one.activity.starting

import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.core.content.ContentProviderCompat.requireContext
import androidx.core.net.toUri
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.viewbinding.ViewBinding
import com.applovin.mediation.nativeAds.MaxNativeAdLoader
import com.applovin.mediation.nativeAds.MaxNativeAdView
import com.applovin.mediation.nativeAds.MaxNativeAdViewBinder
import com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity
import com.tqhit.battery.one.R
import com.tqhit.battery.one.activity.main.MainActivity
import com.tqhit.battery.one.ads.core.ApplovinInterstitialAdManager
import com.tqhit.battery.one.ads.core.ApplovinNativeAdManager
import com.tqhit.battery.one.databinding.ActivityStartingBinding
import com.tqhit.battery.one.databinding.ItemSlideLayout1Binding
import com.tqhit.battery.one.databinding.ItemSlideLayout2Binding
import com.tqhit.battery.one.databinding.ItemSlideLayout3Binding
import com.tqhit.battery.one.databinding.ItemSlideLayout4Binding
import com.tqhit.battery.one.databinding.ItemSlideLayout5Binding
import com.tqhit.battery.one.databinding.ItemSlideLayout6Binding
import com.tqhit.battery.one.databinding.ItemSlideLayout7Binding
import com.tqhit.battery.one.dialog.capacity.ChangeCapacityDialog
import com.tqhit.battery.one.utils.PermissionUtils
import com.tqhit.battery.one.viewmodel.AppViewModel
import com.tqhit.battery.one.viewmodel.battery.BatteryViewModel
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import me.tankery.lib.circularseekbar.CircularSeekBar
import javax.inject.Inject

@AndroidEntryPoint
class StartingActivity : AdLibBaseActivity<ActivityStartingBinding>() {
    override val binding by lazy { ActivityStartingBinding.inflate(layoutInflater) }

    @Inject lateinit var applovinInterstitialAdManager: ApplovinInterstitialAdManager
    @Inject lateinit var applovinNativeAdManager: ApplovinNativeAdManager
    private var nativeAdView: MaxNativeAdView? = null

    private val appViewModel: AppViewModel by viewModels()
    private val batteryViewModel: BatteryViewModel by viewModels()

    private val layouts =
            listOf(
                    R.layout.item_slide_layout_1,
                    R.layout.item_slide_layout_2,
                    R.layout.item_slide_layout_3,
                    R.layout.item_slide_layout_4,
                    R.layout.item_slide_layout_5,
                    R.layout.item_slide_layout_6,
                    R.layout.item_slide_layout_7,
            )

    private val views = arrayListOf<ViewBinding>()

    private val startingViewAdapter by lazy { StartingViewAdapter(views.filter { view -> view !is ItemSlideLayout2Binding }.toCollection(ArrayList())) }

    private val permissionLauncher =
            registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
                val layout5Binding = views[4] as ItemSlideLayout5Binding
                if (isGranted) {
                    layout5Binding.switchInfo.isChecked = true
                    layout5Binding.switchInfo.isEnabled = false
                    appViewModel.setChargeAlarmEnabled(true)
                    PermissionUtils.resetNotificationPermissionDeniedCount()
                } else {
                    layout5Binding.switchInfo.isChecked = false
                    layout5Binding.switchInfo.isEnabled = true
                    appViewModel.setChargeAlarmEnabled(false)
                    if (!PermissionUtils.shouldShowRequestPermissionRationale(this)) {
                        PermissionUtils.incrementNotificationPermissionDeniedCount()
                    }
                }
            }

    override fun setupData() {
        super.setupData()

        for (layout in layouts) {
            val binding =
                    when (layout) {
                        R.layout.item_slide_layout_1 ->
                                ItemSlideLayout1Binding.inflate(layoutInflater)
                        R.layout.item_slide_layout_2 ->
                                ItemSlideLayout2Binding.inflate(layoutInflater)
                        R.layout.item_slide_layout_3 ->
                                ItemSlideLayout3Binding.inflate(layoutInflater)
                        R.layout.item_slide_layout_4 ->
                                ItemSlideLayout4Binding.inflate(layoutInflater)
                        R.layout.item_slide_layout_5 ->
                                ItemSlideLayout5Binding.inflate(layoutInflater)
                        R.layout.item_slide_layout_6 ->
                                ItemSlideLayout6Binding.inflate(layoutInflater)
                        R.layout.item_slide_layout_7 ->
                                ItemSlideLayout7Binding.inflate(layoutInflater)
                        else -> break
                    }
            views.add(binding)
        }

        val layout2Binding = views[1] as ItemSlideLayout2Binding
        val privacyPolicyAccepted = appViewModel.isPrivacyPolicyAccepted()
        layout2Binding.confirmAndContinueButton.visibility =
                privacyPolicyAccepted.let { if (it) View.GONE else View.VISIBLE }
        layout2Binding.underText.text =
                privacyPolicyAccepted.let {
                    if (it) getString(R.string.confirmed)
                    else getString(R.string.privacy_policy_starting)
                }

        val layout5Binding = views[4] as ItemSlideLayout5Binding
        layout5Binding.switchInfo.isChecked = PermissionUtils.isNotificationPermissionGranted(this)
        appViewModel.setChargeAlarmEnabled(layout5Binding.switchInfo.isChecked)
        layout5Binding.switchInfo.isEnabled = !layout5Binding.switchInfo.isChecked
        layout5Binding.textPercent.text = buildString {
            append(appViewModel.getChargeAlarmPercent())
            append("%")
        }
        layout5Binding.circularbar.progress = appViewModel.getChargeAlarmPercent().toFloat()

        // Fill in device information in layout 7
        val layout7Binding = views[6] as ItemSlideLayout7Binding
        // Set device name
        val deviceName =
                if (Build.MODEL.lowercase().startsWith(Build.MANUFACTURER.lowercase())) {
                    Build.MODEL
                } else {
                    "${Build.MANUFACTURER} ${Build.MODEL}"
                }
        layout7Binding.deviceName.text = deviceName

        // Observe battery capacity changes regardless of charging state
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                batteryViewModel.batteryCapacity.collect { capacity ->
                    layout7Binding.capacity.text = buildString {
                        append(capacity)
                        append(getString(R.string.ma))
                    }
                }
            }
        }

        // Observe charging state and update UI accordingly
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                batteryViewModel.isCharging.collect { isCharging ->
                    val isDischarging = !isCharging
                    layout7Binding.discharging.text =
                            if (isDischarging) getString(R.string.yes) else getString(R.string.no)

                    // Show text5 only when discharging
                    layout7Binding.text5.visibility = if (isDischarging) View.GONE else View.VISIBLE

                    // Update UI based on discharging state
                    if (isDischarging) {
                        // Set polarity
                        layout7Binding.polarity.text = batteryViewModel.getBatteryPolarity()
                        // Set measurement parameter
                        layout7Binding.parameter.text = getString(R.string.mA)
                        // Show button and hide progress when discharging
                        layout7Binding.button.visibility = View.VISIBLE
                        layout7Binding.progressbar2.visibility = View.GONE
                        // Enable start button when discharging
                        layout7Binding.startMainActivity.isEnabled = true
                    } else {
                        // When not discharging, hide diagnostics
                        layout7Binding.polarity.text = getString(R.string.not_identified)
                        layout7Binding.parameter.text = getString(R.string.not_identified)
                        // Show progress and hide button when not discharging
                        layout7Binding.button.visibility = View.GONE
                        layout7Binding.progressbar2.visibility = View.VISIBLE
                        // Disable start button when not discharging
                        layout7Binding.startMainActivity.isEnabled = false
                    }
                }
            }
        }
    }

    override fun setupUI() {
        super.setupUI()

        binding.slidePager.adapter = startingViewAdapter
        binding.springDotsIndicator.attachTo(binding.slidePager)
        setupNativeAd()

    }

    private fun setupNativeAd(){
        val container = binding.nativeAd
        val nativeAdView = createNativeAdView()

        // Defer native ad loading to avoid blocking UI startup
        Handler(Looper.getMainLooper()).postDelayed({
            try {
                applovinNativeAdManager.loadNativeAd(
                    nativeAdView = nativeAdView,
                    onAdLoaded = {
                        container.removeAllViews()
                        container.hideShimmer()
                        container.addView(it)
                        Log.d("NativeAd", "Native ad loaded successfully in StartingActivity")
                    },
                    onAdLoadFailed = { errorMsg ->
                        Log.e("NativeAd", "Failed to load native ad in StartingActivity: $errorMsg")
                        container.hideShimmer()
                    }
                )
            } catch (e: Exception) {
                Log.e("NativeAd", "Error loading native ad, MAX SDK may not be ready yet", e)
                container.hideShimmer()
            }
        }, 3000) // 3 second delay to allow MAX SDK initialization
    }

    private fun createNativeAdView(): MaxNativeAdView
    {
        val binder: MaxNativeAdViewBinder =
            MaxNativeAdViewBinder.Builder(R.layout.layout_native_ads)
                .setTitleTextViewId(R.id.title_text_view)
                .setBodyTextViewId(R.id.body_text_view)
                .setStarRatingContentViewGroupId(R.id.star_rating_view )
                .setAdvertiserTextViewId(R.id.advertiser_text_view)
                .setIconImageViewId(R.id.icon_image_view)
                .setMediaContentViewGroupId(R.id.media_view_container)
                .setOptionsContentViewGroupId(R.id.ad_options_view)
                .setCallToActionButtonId(R.id.cta_button)
                .build()
        return MaxNativeAdView(binder, this)
    }






    override fun setupListener() {
        super.setupListener()

        val layout1Binding = views[0] as ItemSlideLayout1Binding
        layout1Binding.nextPage.setOnClickListener {
            binding.slidePager.setCurrentItem(binding.slidePager.currentItem + 1, true)
        }

        val layout2Binding = views[1] as ItemSlideLayout2Binding
        layout2Binding.confirmAndContinueButton.setOnClickListener {
            appViewModel.acceptPrivacyPolicy()
            layout2Binding.confirmAndContinueButton.visibility = View.GONE
            layout2Binding.underText.text = getString(R.string.confirmed)
        }
        layout2Binding.privacyPolicyButton.setOnClickListener {
            startActivity(Intent(Intent.ACTION_VIEW, appViewModel.getPrivacyPolicyUrl().toUri()))
        }
        layout2Binding.nextPage.setOnClickListener {
            binding.slidePager.setCurrentItem(binding.slidePager.currentItem + 1, true)
        }

        val layout3Binding = views[2] as ItemSlideLayout3Binding
        layout3Binding.nextPage.setOnClickListener {
            binding.slidePager.setCurrentItem(binding.slidePager.currentItem + 1, true)
        }

        val layout4Binding = views[3] as ItemSlideLayout4Binding
        layout4Binding.nextPage.setOnClickListener {
            binding.slidePager.setCurrentItem(binding.slidePager.currentItem + 1, true)
        }

        val layout5Binding = views[4] as ItemSlideLayout5Binding
        layout5Binding.switchInfo.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                PermissionUtils.requestNotificationPermission(
                        context = this,
                        permissionLauncher = permissionLauncher,
                        onPermissionGranted = {
                            layout5Binding.switchInfo.isChecked = true
                            layout5Binding.switchInfo.isEnabled = false
                            appViewModel.setChargeAlarmEnabled(true)
                        },
                        onPermissionDenied = {
                            layout5Binding.switchInfo.isChecked = false
                            layout5Binding.switchInfo.isEnabled = true
                            appViewModel.setChargeAlarmEnabled(false)
                        }
                )
            }
        }

        layout5Binding.circularbar.setOnSeekBarChangeListener(
                object : CircularSeekBar.OnCircularSeekBarChangeListener {
                    override fun onProgressChanged(
                            circularSeekBar: CircularSeekBar?,
                            progress: Float,
                            fromUser: Boolean
                    ) {
                        // Map 0-100 range to 60-100 range
                        val mappedPercent = 60 + (progress * 0.4).toInt()
                        layout5Binding.textPercent.text = buildString {
                            append(mappedPercent)
                            append("%")
                        }
                        appViewModel.setChargeAlarmPercent(mappedPercent)
                    }

                    override fun onStartTrackingTouch(seekBar: CircularSeekBar?) {}

                    override fun onStopTrackingTouch(seekBar: CircularSeekBar?) {}
                }
        )
        layout5Binding.nextPage.setOnClickListener {
            binding.slidePager.setCurrentItem(binding.slidePager.currentItem + 1, true)
        }

        val layout6Binding = views[5] as ItemSlideLayout6Binding
        layout6Binding.dontkillmyappButton.setOnClickListener {
            startActivity(Intent(Intent.ACTION_VIEW, appViewModel.getDoNotKillMyAppUrl().toUri()))
        }
        layout6Binding.workInBackgroundPermission.setOnClickListener {
            requestBatteryOptimizationPermission()
        }
        layout6Binding.nextPage.setOnClickListener {
            binding.slidePager.setCurrentItem(binding.slidePager.currentItem + 1, true)
        }

        val layout7Binding = views[6] as ItemSlideLayout7Binding
        layout7Binding.startMainActivity.setOnClickListener {
            appViewModel.setShowedStartPage(true)
            startActivity(Intent(this, MainActivity::class.java))
            finish()
        }

        layout7Binding.changeCapacity.setOnClickListener {
            applovinInterstitialAdManager.showInterstitialAd(
                "default_iv",
                this@StartingActivity
            ) {
                ChangeCapacityDialog(this, batteryViewModel).show()
            }
        }

        binding.slidePager.clearOnPageChangeListeners()
        binding.slidePager.addOnPageChangeListener(
                object : androidx.viewpager.widget.ViewPager.OnPageChangeListener {
                    override fun onPageScrolled(
                            position: Int,
                            positionOffset: Float,
                            positionOffsetPixels: Int
                    ) {}

                    override fun onPageSelected(position: Int) {
                        if (views[position] is ItemSlideLayout2Binding && position > 1 && !appViewModel.isPrivacyPolicyAccepted()) {
                            binding.slidePager.setCurrentItem(1, true)
                        }
                    }

                    override fun onPageScrollStateChanged(state: Int) {}
                }
        )
    }

    private fun requestBatteryOptimizationPermission() {
        try {
            if (batteryViewModel.isIgnoringBatteryOptimizations()) {
                Toast.makeText(this, R.string.permission_granted, Toast.LENGTH_SHORT).show()
                val layout6Binding = views[5] as ItemSlideLayout6Binding
                layout6Binding.workInBackgroundPermission.isEnabled = false
                layout6Binding.workInBackgroundPermission.text =
                        getString(R.string.permission_granted)
                return
            }

            val intent =
                    Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                        data = "package:$packageName".toUri()
                    }
            startActivity(intent)
        } catch (e: Exception) {
            Toast.makeText(this, R.string.unexpected_error, Toast.LENGTH_SHORT).show()
        }
    }

    override fun onResume() {
        super.onResume()

        val layout5Binding = views[4] as ItemSlideLayout5Binding
        layout5Binding.switchInfo.isChecked = PermissionUtils.isNotificationPermissionGranted(this)
        layout5Binding.switchInfo.isEnabled = !layout5Binding.switchInfo.isChecked
        appViewModel.setChargeAlarmEnabled(layout5Binding.switchInfo.isChecked)

        val layout6Binding = views[5] as ItemSlideLayout6Binding
        if (batteryViewModel.isIgnoringBatteryOptimizations()) {
            layout6Binding.workInBackgroundPermission.isEnabled = false
            layout6Binding.workInBackgroundPermission.text = getString(R.string.permission_granted)
        }
    }

    override fun onDestroy() {
        applovinNativeAdManager.destroy()
        super.onDestroy()
    }
}
