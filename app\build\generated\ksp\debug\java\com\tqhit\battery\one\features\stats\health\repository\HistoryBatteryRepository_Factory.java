package com.tqhit.battery.one.features.stats.health.repository;

import com.tqhit.adlib.sdk.data.local.PreferencesHelper;
import com.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class HistoryBatteryRepository_Factory implements Factory<HistoryBatteryRepository> {
  private final Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider;

  private final Provider<PreferencesHelper> preferencesHelperProvider;

  public HistoryBatteryRepository_Factory(
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider,
      Provider<PreferencesHelper> preferencesHelperProvider) {
    this.coreBatteryStatsProvider = coreBatteryStatsProvider;
    this.preferencesHelperProvider = preferencesHelperProvider;
  }

  @Override
  public HistoryBatteryRepository get() {
    return newInstance(coreBatteryStatsProvider.get(), preferencesHelperProvider.get());
  }

  public static HistoryBatteryRepository_Factory create(
      Provider<CoreBatteryStatsProvider> coreBatteryStatsProvider,
      Provider<PreferencesHelper> preferencesHelperProvider) {
    return new HistoryBatteryRepository_Factory(coreBatteryStatsProvider, preferencesHelperProvider);
  }

  public static HistoryBatteryRepository newInstance(
      CoreBatteryStatsProvider coreBatteryStatsProvider, PreferencesHelper preferencesHelper) {
    return new HistoryBatteryRepository(coreBatteryStatsProvider, preferencesHelper);
  }
}
