@echo off
REM Test script for TJ_BatteryOne Enhanced Splash Activity with Asynchronous MAX SDK Initialization
REM Application ID: com.fc.p.tj.charginganimation.batterycharging.chargeeffect

echo ========================================
echo TJ_BatteryOne Splash Initialization Test
echo ========================================

set ADB_PATH=E:\IDE\Android\SDK\platform-tools\adb.exe
set APP_ID=com.fc.p.tj.charginganimation.batterycharging.chargeeffect

echo.
echo Checking ADB connection...
%ADB_PATH% devices

echo.
echo Stopping app if running...
%ADB_PATH% shell am force-stop %APP_ID%

echo.
echo Clearing app data for clean test...
%ADB_PATH% shell pm clear %APP_ID%

echo.
echo Starting cold start performance test...
echo Measuring startup time with enhanced splash...

%ADB_PATH% shell am start -W -n %APP_ID%/.activity.splash.SplashActivity

echo.
echo Monitoring initialization logs...
echo Press Ctrl+C to stop monitoring
echo.

REM Monitor specific log tags for initialization progress
%ADB_PATH% logcat -s STARTUP_TIMING:D SPLASH_PROGRESS:D MAX_INIT:D AD_ADAPTER_LOAD:D InitProgressManager:D SplashActivity:D ServiceInitHelper:D

pause
