package com.tqhit.battery.one.features.navigation;

import dagger.internal.DaggerGenerated;
import dagger.internal.IdentifierNameString;
import dagger.internal.KeepFieldType;
import javax.annotation.processing.Generated;

@IdentifierNameString
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class SharedNavigationViewModel_HiltModules_BindsModule_Binds_LazyMapKey {
  @KeepFieldType
  static SharedNavigationViewModel keepFieldType;

  public static String lazyClassKeyName = "com.tqhit.battery.one.features.navigation.SharedNavigationViewModel";
}
