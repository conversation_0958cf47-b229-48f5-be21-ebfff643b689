// Generated by view binder compiler. Do not edit!
package com.tqhit.battery.one.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.tqhit.battery.one.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivitySplashBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final ImageView ivLogo;

  @NonNull
  public final ProgressBar progressBarInitialization;

  @NonNull
  public final LinearLayout progressContainer;

  @NonNull
  public final TextView tvAppName;

  @NonNull
  public final TextView tvInitializationStatus;

  @NonNull
  public final TextView tvProgressPercentage;

  private ActivitySplashBinding(@NonNull ConstraintLayout rootView, @NonNull ImageView ivLogo,
      @NonNull ProgressBar progressBarInitialization, @NonNull LinearLayout progressContainer,
      @NonNull TextView tvAppName, @NonNull TextView tvInitializationStatus,
      @NonNull TextView tvProgressPercentage) {
    this.rootView = rootView;
    this.ivLogo = ivLogo;
    this.progressBarInitialization = progressBarInitialization;
    this.progressContainer = progressContainer;
    this.tvAppName = tvAppName;
    this.tvInitializationStatus = tvInitializationStatus;
    this.tvProgressPercentage = tvProgressPercentage;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySplashBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySplashBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_splash, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySplashBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ivLogo;
      ImageView ivLogo = ViewBindings.findChildViewById(rootView, id);
      if (ivLogo == null) {
        break missingId;
      }

      id = R.id.progressBarInitialization;
      ProgressBar progressBarInitialization = ViewBindings.findChildViewById(rootView, id);
      if (progressBarInitialization == null) {
        break missingId;
      }

      id = R.id.progressContainer;
      LinearLayout progressContainer = ViewBindings.findChildViewById(rootView, id);
      if (progressContainer == null) {
        break missingId;
      }

      id = R.id.tvAppName;
      TextView tvAppName = ViewBindings.findChildViewById(rootView, id);
      if (tvAppName == null) {
        break missingId;
      }

      id = R.id.tvInitializationStatus;
      TextView tvInitializationStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvInitializationStatus == null) {
        break missingId;
      }

      id = R.id.tvProgressPercentage;
      TextView tvProgressPercentage = ViewBindings.findChildViewById(rootView, id);
      if (tvProgressPercentage == null) {
        break missingId;
      }

      return new ActivitySplashBinding((ConstraintLayout) rootView, ivLogo,
          progressBarInitialization, progressContainer, tvAppName, tvInitializationStatus,
          tvProgressPercentage);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
