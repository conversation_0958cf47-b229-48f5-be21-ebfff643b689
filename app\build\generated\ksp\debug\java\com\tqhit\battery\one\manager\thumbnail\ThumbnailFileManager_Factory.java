package com.tqhit.battery.one.manager.thumbnail;

import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ThumbnailFileManager_Factory implements Factory<ThumbnailFileManager> {
  private final Provider<Context> contextProvider;

  public ThumbnailFileManager_Factory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public ThumbnailFileManager get() {
    return newInstance(contextProvider.get());
  }

  public static ThumbnailFileManager_Factory create(Provider<Context> contextProvider) {
    return new ThumbnailFileManager_Factory(contextProvider);
  }

  public static ThumbnailFileManager newInstance(Context context) {
    return new ThumbnailFileManager(context);
  }
}
