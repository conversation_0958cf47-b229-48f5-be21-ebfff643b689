package com.tqhit.battery.one.manager.discharge;

import com.tqhit.adlib.sdk.data.local.PreferencesHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class DischargeSessionManager_Factory implements Factory<DischargeSessionManager> {
  private final Provider<PreferencesHelper> preferencesHelperProvider;

  public DischargeSessionManager_Factory(Provider<PreferencesHelper> preferencesHelperProvider) {
    this.preferencesHelperProvider = preferencesHelperProvider;
  }

  @Override
  public DischargeSessionManager get() {
    return newInstance(preferencesHelperProvider.get());
  }

  public static DischargeSessionManager_Factory create(
      Provider<PreferencesHelper> preferencesHelperProvider) {
    return new DischargeSessionManager_Factory(preferencesHelperProvider);
  }

  public static DischargeSessionManager newInstance(PreferencesHelper preferencesHelper) {
    return new DischargeSessionManager(preferencesHelper);
  }
}
