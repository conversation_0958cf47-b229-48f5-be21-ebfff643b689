package com.tqhit.battery.one.manager.graph;

import com.tqhit.adlib.sdk.data.local.PreferencesHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class TemperatureHistoryManager_Factory implements Factory<TemperatureHistoryManager> {
  private final Provider<PreferencesHelper> preferencesHelperProvider;

  public TemperatureHistoryManager_Factory(Provider<PreferencesHelper> preferencesHelperProvider) {
    this.preferencesHelperProvider = preferencesHelperProvider;
  }

  @Override
  public TemperatureHistoryManager get() {
    return newInstance(preferencesHelperProvider.get());
  }

  public static TemperatureHistoryManager_Factory create(
      Provider<PreferencesHelper> preferencesHelperProvider) {
    return new TemperatureHistoryManager_Factory(preferencesHelperProvider);
  }

  public static TemperatureHistoryManager newInstance(PreferencesHelper preferencesHelper) {
    return new TemperatureHistoryManager(preferencesHelper);
  }
}
