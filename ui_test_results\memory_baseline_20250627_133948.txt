Applications Memory Usage (in Kilobytes):
Uptime: 5276287 Realtime: 5276287

** MEMINFO in pid 9247 [com.fc.p.tj.charginganimation.batterycharging.chargeeffect] **
                   Pss  Private  Private  SwapPss      Rss     Heap     Heap     Heap
                 Total    Dirty    Clean    Dirty    Total     Size    Alloc     Free
                ------   ------   ------   ------   ------   ------   ------   ------
  Native Heap    36006    35988        4       31    36932    50592    42785     3962
  Dalvik Heap    21692    21656       12       87    22772    40812    20667    20145
 Dalvik Other    32971    26196        0        0    40048                           
        Stack    10436    10436        0        0    10440                           
       Ashmem      347      240        0        0     1180                           
    Other dev      141        4      136        0      456                           
     .so mmap     7983      936      560       14    43284                           
    .jar mmap     6166        0     2920        0    51468                           
    .apk mmap    28309      600     4220        0    97840                           
    .ttf mmap      337        0       80        0     1280                           
    .dex mmap      811        0      100        0     4064                           
    .oat mmap       46        0       12        0     2240                           
    .art mmap    11584     9220     1912       72    26420                           
   Other mmap     5124      180      368        0    12808                           
      Unknown   114100   114088        8        0   114556                           
        TOTAL   276257   219544    10332      204   465788    91404    63452    24107
 
 App Summary
                       Pss(KB)                        Rss(KB)
                        ------                         ------
           Java Heap:    32788                          49192
         Native Heap:    35988                          36932
                Code:     9684                         213980
               Stack:    10436                          10440
            Graphics:        0                              0
       Private Other:   140980
              System:    46381
             Unknown:                                  155244
 
           TOTAL PSS:   276257            TOTAL RSS:   465788       TOTAL SWAP PSS:      204
 
 Objects
               Views:      193         ViewRootImpl:        1
         AppContexts:       26           Activities:        1
              Assets:       30        AssetManagers:        0
       Local Binders:      177        Proxy Binders:      101
       Parcel memory:      130         Parcel count:      370
    Death Recipients:       15             WebViews:        3
 
 Native Allocations
                         Count                       Total(kB)
                        ------                         ------
    Other (malloced):     1283                            121
 Other (nonmalloced):      356                            213
 
 SQL
         MEMORY_USED:     1895
  PAGECACHE_OVERFLOW:      814          MALLOC_SIZE:       46
 
 DATABASES
      pgsz     dbsz   Lookaside(b) cache hits cache misses cache size  Dbname
PER CONNECTION STATS
         4       24             82    12    46     7  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/track_manager_metrics_sdk.db
         4       52            123   145    55    18  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/com.im_10.8.3.db
         4       44             58     3    37     6  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/bigo_ads_sdk.db
         4       40            109    20    58    11  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/chartboost_exoplayer.db
         4       36            123    91    93    24  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/component_20799a27-fa80-4b36-b2db-0f8141f24180.db
         4       24             18     8    52     1  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/metrics-db
         4        8                    0     0     0    (attached) temp
         4       36            115    98   108    25  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/component_main.db
         4       20             42     1    28     5  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/auto_inapp.db
         4       20             24     0    26     3  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/npth_log.db
         4       36            123    51    75    22  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/component_322a737a-a0ca-44e0-bc85-649b1c7c1db6.db
         4      100             18    12    83     1  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/androidx.work.workdb
         4        8                    0     0     0    (attached) temp
         4      100             16     4    15     1  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/androidx.work.workdb (3)
         4       84            106    10    52    11  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/ttopensdk.db
         4       56             92    35    82    10  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/com.google.android.datatransport.events
         4       32             93   101    92    11  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/service_main.db
POOL STATS
     cache hits  cache misses    cache size  Dbname
             12            47            59  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/track_manager_metrics_sdk.db
            145            56           201  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/com.im_10.8.3.db
              3            38            41  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/bigo_ads_sdk.db
             15            64            79  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/chartboost_exoplayer.db
             91            94           185  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/component_20799a27-fa80-4b36-b2db-0f8141f24180.db
              9            84            93  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/metrics-db
             98           109           207  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/component_main.db
              1            29            30  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/auto_inapp.db
              0            27            27  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/npth_log.db
             51            76           127  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/component_322a737a-a0ca-44e0-bc85-649b1c7c1db6.db
             17           132           149  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/androidx.work.workdb
             10            53            63  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/ttopensdk.db
             35            83           118  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/com.google.android.datatransport.events
            101            93           194  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/service_main.db
