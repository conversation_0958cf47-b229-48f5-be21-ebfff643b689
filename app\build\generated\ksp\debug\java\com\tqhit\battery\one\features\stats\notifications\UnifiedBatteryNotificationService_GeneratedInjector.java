package com.tqhit.battery.one.features.stats.notifications;

import dagger.hilt.InstallIn;
import dagger.hilt.android.components.ServiceComponent;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.internal.GeneratedEntryPoint;
import javax.annotation.processing.Generated;

@OriginatingElement(
    topLevelClass = UnifiedBatteryNotificationService.class
)
@GeneratedEntryPoint
@InstallIn(ServiceComponent.class)
@Generated("dagger.hilt.android.processor.internal.androidentrypoint.InjectorEntryPointGenerator")
public interface UnifiedBatteryNotificationService_GeneratedInjector {
  void injectUnifiedBatteryNotificationService(
      UnifiedBatteryNotificationService unifiedBatteryNotificationService);
}
