package com.tqhit.battery.one.manager.charge;

import com.tqhit.adlib.sdk.data.local.PreferencesHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ChargingSessionManager_Factory implements Factory<ChargingSessionManager> {
  private final Provider<PreferencesHelper> preferencesHelperProvider;

  public ChargingSessionManager_Factory(Provider<PreferencesHelper> preferencesHelperProvider) {
    this.preferencesHelperProvider = preferencesHelperProvider;
  }

  @Override
  public ChargingSessionManager get() {
    return newInstance(preferencesHelperProvider.get());
  }

  public static ChargingSessionManager_Factory create(
      Provider<PreferencesHelper> preferencesHelperProvider) {
    return new ChargingSessionManager_Factory(preferencesHelperProvider);
  }

  public static ChargingSessionManager newInstance(PreferencesHelper preferencesHelper) {
    return new ChargingSessionManager(preferencesHelper);
  }
}
