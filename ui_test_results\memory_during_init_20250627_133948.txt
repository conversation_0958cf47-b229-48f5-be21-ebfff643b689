Applications Memory Usage (in Kilobytes):
Uptime: 5279137 Realtime: 5279137

** MEMINFO in pid 9247 [com.fc.p.tj.charginganimation.batterycharging.chargeeffect] **
                   Pss  Private  Private  SwapPss      Rss     Heap     Heap     Heap
                 Total    Dirty    Clean    Dirty    Total     Size    Alloc     Free
                ------   ------   ------   ------   ------   ------   ------   ------
  Native Heap    41315    41296        4       31    42240    57048    48912     3835
  Dalvik Heap    61994    56980     4976       19    63088    52113    27537    24576
 Dalvik Other    34653    27304       48        0    42252                           
        Stack    11676    11676        0        0    11680                           
       Ashmem      333      184        0        0     1208                           
    Other dev      141        4      136        0      456                           
     .so mmap     7978      936      560       14    43296                           
    .jar mmap     6191        0     2940        0    51536                           
    .apk mmap    32174      600     6072        0   106888                           
    .ttf mmap      337        0       80        0     1280                           
    .dex mmap      843        0      100        0     4184                           
    .oat mmap       46        0       12        0     2240                           
    .art mmap    13310     9264     3524       51    28012                           
   Other mmap     5475      372      304        0    13408                           
      Unknown   119808   119796        8        0   120264                           
        TOTAL   336389   268412    18764      115   532032   109161    76449    28411
 
 App Summary
                       Pss(KB)                        Rss(KB)
                        ------                         ------
           Java Heap:    69768                          91100
         Native Heap:    41296                          42240
                Code:    11632                         224356
               Stack:    11676                          11680
            Graphics:        0                              0
       Private Other:   152804
              System:    49213
             Unknown:                                  162656
 
           TOTAL PSS:   336389            TOTAL RSS:   532032       TOTAL SWAP PSS:      115
 
 Objects
               Views:      194         ViewRootImpl:        1
         AppContexts:       27           Activities:        1
              Assets:       30        AssetManagers:        0
       Local Binders:      185        Proxy Binders:      110
       Parcel memory:      401         Parcel count:      977
    Death Recipients:       14             WebViews:        3
 
 Native Allocations
                         Count                       Total(kB)
                        ------                         ------
    Other (malloced):     1281                            121
 Other (nonmalloced):      354                            216
   Bitmap (malloced):        1                              1
 
 SQL
         MEMORY_USED:     2079
  PAGECACHE_OVERFLOW:      920          MALLOC_SIZE:       46
 
 DATABASES
      pgsz     dbsz   Lookaside(b) cache hits cache misses cache size  Dbname
PER CONNECTION STATS
         4       32             85    23    62     8  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/track_manager_metrics_sdk.db
         4       52            123   208    63    19  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/com.im_10.8.3.db
         4       44             95    24    43     9  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/bigo_ads_sdk.db
         4       40            109    20    61    11  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/chartboost_exoplayer.db
         4       36            123    91    96    24  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/component_20799a27-fa80-4b36-b2db-0f8141f24180.db
         4       24             18    18    93     1  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/metrics-db
         4        8                    0     0     0    (attached) temp
         4       36            115    98   111    25  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/component_main.db
         4       20             42     1    31     5  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/auto_inapp.db
         4       96             45     2    62     5  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/mbridge.msdk.db
         4       20             24     0    29     3  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/npth_log.db
         4       36            123    51    78    22  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/component_322a737a-a0ca-44e0-bc85-649b1c7c1db6.db
         4      100             18    40   146     1  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/androidx.work.workdb
         4        8                    0     0     0    (attached) temp
         4      100             20     5    19     1  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/androidx.work.workdb (3)
         4       84            112    13    58    12  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/ttopensdk.db
         4       56             92    35    85    10  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/com.google.android.datatransport.events
         4       32             93   101    95    11  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/service_main.db
POOL STATS
     cache hits  cache misses    cache size  Dbname
             23            63            86  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/track_manager_metrics_sdk.db
            208            64           272  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/com.im_10.8.3.db
             24            44            68  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/bigo_ads_sdk.db
             15            67            82  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/chartboost_exoplayer.db
             91            97           188  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/component_20799a27-fa80-4b36-b2db-0f8141f24180.db
             19           125           144  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/metrics-db
             98           112           210  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/component_main.db
              1            32            33  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/auto_inapp.db
              2            63            65  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/mbridge.msdk.db
              0            30            30  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/npth_log.db
             51            79           130  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/component_322a737a-a0ca-44e0-bc85-649b1c7c1db6.db
             46           199           245  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/androidx.work.workdb
             13            59            72  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/ttopensdk.db
             35            86           121  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/databases/com.google.android.datatransport.events
            101            96           197  /data/user/0/com.fc.p.tj.charginganimation.batterycharging.chargeeffect/no_backup/appmetrica/analytics/db/service_main.db
