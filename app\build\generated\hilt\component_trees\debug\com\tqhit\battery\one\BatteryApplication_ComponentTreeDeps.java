package com.tqhit.battery.one;

import dagger.hilt.internal.aggregatedroot.codegen._com_tqhit_battery_one_BatteryApplication;
import dagger.hilt.internal.componenttreedeps.ComponentTreeDeps;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ActivityComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ActivityRetainedComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_FragmentComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ServiceComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ViewComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ViewModelComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ViewWithFragmentComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ActivityComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ActivityRetainedComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_FragmentComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ServiceComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ViewComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ViewModelComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ViewWithFragmentComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_components_SingletonComponent;
import hilt_aggregated_deps._com_tqhit_adlib_sdk_di_AdmobModule;
import hilt_aggregated_deps._com_tqhit_adlib_sdk_di_AnalyticsModule;
import hilt_aggregated_deps._com_tqhit_adlib_sdk_di_AppModule;
import hilt_aggregated_deps._com_tqhit_adlib_sdk_di_FirebaseModule;
import hilt_aggregated_deps._com_tqhit_adlib_sdk_di_SharedPreferencesModule;
import hilt_aggregated_deps._com_tqhit_adlib_sdk_ui_main_MainActivity_GeneratedInjector;
import hilt_aggregated_deps._com_tqhit_battery_one_BatteryApplication_GeneratedInjector;
import hilt_aggregated_deps._com_tqhit_battery_one_activity_animation_AnimationActivity_GeneratedInjector;
import hilt_aggregated_deps._com_tqhit_battery_one_activity_main_MainActivity_GeneratedInjector;
import hilt_aggregated_deps._com_tqhit_battery_one_activity_overlay_ChargingOverlayActivity_GeneratedInjector;
import hilt_aggregated_deps._com_tqhit_battery_one_activity_password_EnterPasswordActivity_GeneratedInjector;
import hilt_aggregated_deps._com_tqhit_battery_one_activity_splash_SplashActivity_GeneratedInjector;
import hilt_aggregated_deps._com_tqhit_battery_one_activity_starting_StartingActivity_GeneratedInjector;
import hilt_aggregated_deps._com_tqhit_battery_one_di_ThumbnailPreloadingModule;
import hilt_aggregated_deps._com_tqhit_battery_one_dialog_utils_ApplovinAdEntryPoint;
import hilt_aggregated_deps._com_tqhit_battery_one_features_navigation_SharedNavigationViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_tqhit_battery_one_features_navigation_SharedNavigationViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_tqhit_battery_one_features_navigation_di_NavigationDIModule;
import hilt_aggregated_deps._com_tqhit_battery_one_features_stats_charge_di_StatsChargeDIModule;
import hilt_aggregated_deps._com_tqhit_battery_one_features_stats_charge_presentation_StatsChargeFragment_GeneratedInjector;
import hilt_aggregated_deps._com_tqhit_battery_one_features_stats_charge_presentation_StatsChargeViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_tqhit_battery_one_features_stats_charge_presentation_StatsChargeViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_tqhit_battery_one_features_stats_corebattery_di_CoreBatteryDIModule;
import hilt_aggregated_deps._com_tqhit_battery_one_features_stats_corebattery_service_CoreBatteryStatsService_GeneratedInjector;
import hilt_aggregated_deps._com_tqhit_battery_one_features_stats_discharge_di_StatsDischargeModule;
import hilt_aggregated_deps._com_tqhit_battery_one_features_stats_discharge_di_StatsDischargeProvidersModule;
import hilt_aggregated_deps._com_tqhit_battery_one_features_stats_discharge_presentation_DischargeFragment_GeneratedInjector;
import hilt_aggregated_deps._com_tqhit_battery_one_features_stats_discharge_presentation_DischargeViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_tqhit_battery_one_features_stats_discharge_presentation_DischargeViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_tqhit_battery_one_features_stats_discharge_service_EnhancedDischargeTimerService_GeneratedInjector;
import hilt_aggregated_deps._com_tqhit_battery_one_features_stats_health_di_HealthDIModule;
import hilt_aggregated_deps._com_tqhit_battery_one_features_stats_health_presentation_HealthViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_tqhit_battery_one_features_stats_health_presentation_HealthViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_tqhit_battery_one_features_stats_notifications_UnifiedBatteryNotificationService_GeneratedInjector;
import hilt_aggregated_deps._com_tqhit_battery_one_fragment_main_HealthFragment_GeneratedInjector;
import hilt_aggregated_deps._com_tqhit_battery_one_fragment_main_SettingsFragment_GeneratedInjector;
import hilt_aggregated_deps._com_tqhit_battery_one_fragment_main_animation_AnimationGridFragment_GeneratedInjector;
import hilt_aggregated_deps._com_tqhit_battery_one_fragment_main_others_OthersFragment_GeneratedInjector;
import hilt_aggregated_deps._com_tqhit_battery_one_service_ChargingOverlayService_GeneratedInjector;
import hilt_aggregated_deps._com_tqhit_battery_one_viewmodel_AppViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_tqhit_battery_one_viewmodel_AppViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_tqhit_battery_one_viewmodel_animation_AnimationViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_tqhit_battery_one_viewmodel_animation_AnimationViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._com_tqhit_battery_one_viewmodel_battery_BatteryViewModel_HiltModules_BindsModule;
import hilt_aggregated_deps._com_tqhit_battery_one_viewmodel_battery_BatteryViewModel_HiltModules_KeyModule;
import hilt_aggregated_deps._dagger_hilt_android_flags_FragmentGetContextFix_FragmentGetContextFixEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_flags_HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_DefaultViewModelFactories_ActivityEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_DefaultViewModelFactories_FragmentEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_HiltViewModelFactory_ViewModelFactoriesEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_HiltWrapper_DefaultViewModelFactories_ActivityModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_HiltWrapper_HiltViewModelFactory_ActivityCreatorEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_HiltWrapper_HiltViewModelFactory_ViewModelModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_ActivityComponentManager_ActivityComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_FragmentComponentManager_FragmentComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedLifecycleEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_LifecycleModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_HiltWrapper_SavedStateHandleModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_ServiceComponentManager_ServiceComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_ViewComponentManager_ViewComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_ViewComponentManager_ViewWithFragmentComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_modules_ApplicationContextModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_modules_HiltWrapper_ActivityModule;

@ComponentTreeDeps(
    rootDeps = _com_tqhit_battery_one_BatteryApplication.class,
    defineComponentDeps = {
        _dagger_hilt_android_components_ActivityComponent.class,
        _dagger_hilt_android_components_ActivityRetainedComponent.class,
        _dagger_hilt_android_components_FragmentComponent.class,
        _dagger_hilt_android_components_ServiceComponent.class,
        _dagger_hilt_android_components_ViewComponent.class,
        _dagger_hilt_android_components_ViewModelComponent.class,
        _dagger_hilt_android_components_ViewWithFragmentComponent.class,
        _dagger_hilt_android_internal_builders_ActivityComponentBuilder.class,
        _dagger_hilt_android_internal_builders_ActivityRetainedComponentBuilder.class,
        _dagger_hilt_android_internal_builders_FragmentComponentBuilder.class,
        _dagger_hilt_android_internal_builders_ServiceComponentBuilder.class,
        _dagger_hilt_android_internal_builders_ViewComponentBuilder.class,
        _dagger_hilt_android_internal_builders_ViewModelComponentBuilder.class,
        _dagger_hilt_android_internal_builders_ViewWithFragmentComponentBuilder.class,
        _dagger_hilt_components_SingletonComponent.class
    },
    aggregatedDeps = {
        _com_tqhit_adlib_sdk_di_AdmobModule.class,
        _com_tqhit_adlib_sdk_di_AnalyticsModule.class,
        _com_tqhit_adlib_sdk_di_AppModule.class,
        _com_tqhit_adlib_sdk_di_FirebaseModule.class,
        _com_tqhit_adlib_sdk_di_SharedPreferencesModule.class,
        _com_tqhit_adlib_sdk_ui_main_MainActivity_GeneratedInjector.class,
        _com_tqhit_battery_one_BatteryApplication_GeneratedInjector.class,
        _com_tqhit_battery_one_activity_animation_AnimationActivity_GeneratedInjector.class,
        _com_tqhit_battery_one_activity_main_MainActivity_GeneratedInjector.class,
        _com_tqhit_battery_one_activity_overlay_ChargingOverlayActivity_GeneratedInjector.class,
        _com_tqhit_battery_one_activity_password_EnterPasswordActivity_GeneratedInjector.class,
        _com_tqhit_battery_one_activity_splash_SplashActivity_GeneratedInjector.class,
        _com_tqhit_battery_one_activity_starting_StartingActivity_GeneratedInjector.class,
        _com_tqhit_battery_one_di_ThumbnailPreloadingModule.class,
        _com_tqhit_battery_one_dialog_utils_ApplovinAdEntryPoint.class,
        _com_tqhit_battery_one_features_navigation_SharedNavigationViewModel_HiltModules_BindsModule.class,
        _com_tqhit_battery_one_features_navigation_SharedNavigationViewModel_HiltModules_KeyModule.class,
        _com_tqhit_battery_one_features_navigation_di_NavigationDIModule.class,
        _com_tqhit_battery_one_features_stats_charge_di_StatsChargeDIModule.class,
        _com_tqhit_battery_one_features_stats_charge_presentation_StatsChargeFragment_GeneratedInjector.class,
        _com_tqhit_battery_one_features_stats_charge_presentation_StatsChargeViewModel_HiltModules_BindsModule.class,
        _com_tqhit_battery_one_features_stats_charge_presentation_StatsChargeViewModel_HiltModules_KeyModule.class,
        _com_tqhit_battery_one_features_stats_corebattery_di_CoreBatteryDIModule.class,
        _com_tqhit_battery_one_features_stats_corebattery_service_CoreBatteryStatsService_GeneratedInjector.class,
        _com_tqhit_battery_one_features_stats_discharge_di_StatsDischargeModule.class,
        _com_tqhit_battery_one_features_stats_discharge_di_StatsDischargeProvidersModule.class,
        _com_tqhit_battery_one_features_stats_discharge_presentation_DischargeFragment_GeneratedInjector.class,
        _com_tqhit_battery_one_features_stats_discharge_presentation_DischargeViewModel_HiltModules_BindsModule.class,
        _com_tqhit_battery_one_features_stats_discharge_presentation_DischargeViewModel_HiltModules_KeyModule.class,
        _com_tqhit_battery_one_features_stats_discharge_service_EnhancedDischargeTimerService_GeneratedInjector.class,
        _com_tqhit_battery_one_features_stats_health_di_HealthDIModule.class,
        _com_tqhit_battery_one_features_stats_health_presentation_HealthViewModel_HiltModules_BindsModule.class,
        _com_tqhit_battery_one_features_stats_health_presentation_HealthViewModel_HiltModules_KeyModule.class,
        _com_tqhit_battery_one_features_stats_notifications_UnifiedBatteryNotificationService_GeneratedInjector.class,
        _com_tqhit_battery_one_fragment_main_HealthFragment_GeneratedInjector.class,
        _com_tqhit_battery_one_fragment_main_SettingsFragment_GeneratedInjector.class,
        _com_tqhit_battery_one_fragment_main_animation_AnimationGridFragment_GeneratedInjector.class,
        _com_tqhit_battery_one_fragment_main_others_OthersFragment_GeneratedInjector.class,
        _com_tqhit_battery_one_service_ChargingOverlayService_GeneratedInjector.class,
        _com_tqhit_battery_one_viewmodel_AppViewModel_HiltModules_BindsModule.class,
        _com_tqhit_battery_one_viewmodel_AppViewModel_HiltModules_KeyModule.class,
        _com_tqhit_battery_one_viewmodel_animation_AnimationViewModel_HiltModules_BindsModule.class,
        _com_tqhit_battery_one_viewmodel_animation_AnimationViewModel_HiltModules_KeyModule.class,
        _com_tqhit_battery_one_viewmodel_battery_BatteryViewModel_HiltModules_BindsModule.class,
        _com_tqhit_battery_one_viewmodel_battery_BatteryViewModel_HiltModules_KeyModule.class,
        _dagger_hilt_android_flags_FragmentGetContextFix_FragmentGetContextFixEntryPoint.class,
        _dagger_hilt_android_flags_HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule.class,
        _dagger_hilt_android_internal_lifecycle_DefaultViewModelFactories_ActivityEntryPoint.class,
        _dagger_hilt_android_internal_lifecycle_DefaultViewModelFactories_FragmentEntryPoint.class,
        _dagger_hilt_android_internal_lifecycle_HiltViewModelFactory_ViewModelFactoriesEntryPoint.class,
        _dagger_hilt_android_internal_lifecycle_HiltWrapper_DefaultViewModelFactories_ActivityModule.class,
        _dagger_hilt_android_internal_lifecycle_HiltWrapper_HiltViewModelFactory_ActivityCreatorEntryPoint.class,
        _dagger_hilt_android_internal_lifecycle_HiltWrapper_HiltViewModelFactory_ViewModelModule.class,
        _dagger_hilt_android_internal_managers_ActivityComponentManager_ActivityComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_managers_FragmentComponentManager_FragmentComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedLifecycleEntryPoint.class,
        _dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_LifecycleModule.class,
        _dagger_hilt_android_internal_managers_HiltWrapper_SavedStateHandleModule.class,
        _dagger_hilt_android_internal_managers_ServiceComponentManager_ServiceComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_managers_ViewComponentManager_ViewComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_managers_ViewComponentManager_ViewWithFragmentComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_modules_ApplicationContextModule.class,
        _dagger_hilt_android_internal_modules_HiltWrapper_ActivityModule.class
    }
)
public final class BatteryApplication_ComponentTreeDeps {
}
