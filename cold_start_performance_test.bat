@echo off
REM Cold Start Performance Testing for Enhanced Splash Activity
REM Measures total startup time, splash display timing, and memory usage

echo ========================================
echo Cold Start Performance Testing
echo Target: <3 seconds total, splash <500ms
echo ========================================

set ADB_PATH=E:\IDE\Android\SDK\platform-tools\adb.exe
set APP_ID=com.fc.p.tj.charginganimation.batterycharging.chargeeffect
set TIMESTAMP=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%

REM Create results directory
if not exist cold_start_results mkdir cold_start_results

echo Starting Cold Start Performance Test at %TIMESTAMP%
echo Application ID: %APP_ID%
echo.

REM Test 1: Multiple Cold Start Measurements
echo === Test 1: Cold Start Time Measurements ===
echo Running 5 cold start tests for statistical accuracy...

echo Test Run,TotalTime(ms),WaitTime(ms),ThisTime(ms) > cold_start_results\cold_start_times_%TIMESTAMP%.csv

for /l %%i in (1,1,5) do (
    echo Running cold start test %%i of 5...
    
    REM Force stop and clear app data for true cold start
    %ADB_PATH% shell am force-stop %APP_ID%
    %ADB_PATH% shell pm clear %APP_ID%
    
    REM Wait for system to settle
    timeout /t 3 /nobreak >nul
    
    REM Measure cold start time with detailed output
    echo Cold Start Test %%i: >> cold_start_results\cold_start_detailed_%TIMESTAMP%.txt
    %ADB_PATH% shell am start -W -n %APP_ID%/.activity.splash.SplashActivity >> cold_start_results\cold_start_detailed_%TIMESTAMP%.txt
    echo. >> cold_start_results\cold_start_detailed_%TIMESTAMP%.txt
    
    REM Extract timing data for CSV
    for /f "tokens=2 delims=:" %%a in ('findstr "TotalTime" cold_start_results\cold_start_detailed_%TIMESTAMP%.txt ^| tail -1') do (
        echo %%i,%%a >> cold_start_results\cold_start_times_%TIMESTAMP%.csv
    )
    
    timeout /t 2 /nobreak >nul
)

echo Cold start measurements completed.
echo.

REM Test 2: Splash Screen Display Timing
echo === Test 2: Splash Screen Display Timing ===
echo Measuring time from app launch to splash screen visibility...

%ADB_PATH% shell am force-stop %APP_ID%
%ADB_PATH% shell pm clear %APP_ID%
timeout /t 2 /nobreak >nul

REM Monitor splash screen timing
start /b %ADB_PATH% logcat -s STARTUP_TIMING:D SPLASH_PROGRESS:D SplashActivity:D > cold_start_results\splash_timing_%TIMESTAMP%.log

echo Launching app for splash timing measurement...
set START_TIME=%time%
%ADB_PATH% shell am start -n %APP_ID%/.activity.splash.SplashActivity

echo Monitoring splash screen for 10 seconds...
timeout /t 10 /nobreak >nul

taskkill /f /im adb.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo Splash timing test completed.
echo.

REM Test 3: Memory Usage During Cold Start
echo === Test 3: Memory Usage Analysis ===
echo Monitoring memory consumption during cold start...

%ADB_PATH% shell am force-stop %APP_ID%
timeout /t 2 /nobreak >nul

echo Capturing baseline memory state...
%ADB_PATH% shell dumpsys meminfo > cold_start_results\system_memory_baseline_%TIMESTAMP%.txt

echo Launching app and monitoring memory...
%ADB_PATH% shell am start -n %APP_ID%/.activity.splash.SplashActivity

REM Capture memory at different stages
timeout /t 1 /nobreak >nul
%ADB_PATH% shell dumpsys meminfo %APP_ID% > cold_start_results\app_memory_1s_%TIMESTAMP%.txt

timeout /t 2 /nobreak >nul
%ADB_PATH% shell dumpsys meminfo %APP_ID% > cold_start_results\app_memory_3s_%TIMESTAMP%.txt

timeout /t 2 /nobreak >nul
%ADB_PATH% shell dumpsys meminfo %APP_ID% > cold_start_results\app_memory_5s_%TIMESTAMP%.txt

echo Memory analysis completed.
echo.

REM Test 4: Background Thread Activity
echo === Test 4: Background Thread Activity ===
echo Monitoring background initialization threads...

%ADB_PATH% shell am force-stop %APP_ID%
timeout /t 2 /nobreak >nul

start /b %ADB_PATH% logcat -s InitProgressManager:D ServiceInitHelper:D BatteryApplication:D > cold_start_results\background_activity_%TIMESTAMP%.log

echo Launching app for background thread monitoring...
%ADB_PATH% shell am start -n %APP_ID%/.activity.splash.SplashActivity

echo Monitoring background activity for 12 seconds...
timeout /t 12 /nobreak >nul

taskkill /f /im adb.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo Background thread monitoring completed.
echo.

REM Test 5: UI Thread Blocking Analysis
echo === Test 5: UI Thread Blocking Analysis ===
echo Checking for ANR conditions and main thread blocking...

%ADB_PATH% shell am force-stop %APP_ID%
timeout /t 2 /nobreak >nul

start /b %ADB_PATH% logcat -s ActivityManager:W Choreographer:W ANR:W > cold_start_results\ui_blocking_%TIMESTAMP%.log

echo Launching app for UI thread analysis...
%ADB_PATH% shell am start -n %APP_ID%/.activity.splash.SplashActivity

echo Monitoring for UI blocking for 10 seconds...
timeout /t 10 /nobreak >nul

taskkill /f /im adb.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo UI thread analysis completed.
echo.

REM Analyze Results
echo === Analyzing Cold Start Performance Results ===

echo Calculating cold start statistics...
if exist cold_start_results\cold_start_times_%TIMESTAMP%.csv (
    echo Cold Start Performance Analysis > cold_start_results\performance_analysis_%TIMESTAMP%.txt
    echo ================================= >> cold_start_results\performance_analysis_%TIMESTAMP%.txt
    echo. >> cold_start_results\performance_analysis_%TIMESTAMP%.txt
    
    echo Raw Cold Start Times: >> cold_start_results\performance_analysis_%TIMESTAMP%.txt
    type cold_start_results\cold_start_times_%TIMESTAMP%.csv >> cold_start_results\performance_analysis_%TIMESTAMP%.txt
    echo. >> cold_start_results\performance_analysis_%TIMESTAMP%.txt
)

echo Analyzing splash screen timing...
if exist cold_start_results\splash_timing_%TIMESTAMP%.log (
    echo Splash Screen Timing Analysis: >> cold_start_results\performance_analysis_%TIMESTAMP%.txt
    echo First splash log entry: >> cold_start_results\performance_analysis_%TIMESTAMP%.txt
    findstr /c:"SplashActivity.onCreate" cold_start_results\splash_timing_%TIMESTAMP%.log | head -1 >> cold_start_results\performance_analysis_%TIMESTAMP%.txt
    echo Progress tracking setup: >> cold_start_results\performance_analysis_%TIMESTAMP%.txt
    findstr /c:"Progress tracking setup" cold_start_results\splash_timing_%TIMESTAMP%.log >> cold_start_results\performance_analysis_%TIMESTAMP%.txt
    echo Initialization completion: >> cold_start_results\performance_analysis_%TIMESTAMP%.txt
    findstr /c:"Total initialization time" cold_start_results\splash_timing_%TIMESTAMP%.log >> cold_start_results\performance_analysis_%TIMESTAMP%.txt
    echo. >> cold_start_results\performance_analysis_%TIMESTAMP%.txt
)

echo Analyzing memory usage...
if exist cold_start_results\app_memory_5s_%TIMESTAMP%.txt (
    echo Memory Usage Analysis: >> cold_start_results\performance_analysis_%TIMESTAMP%.txt
    echo Memory at 1 second: >> cold_start_results\performance_analysis_%TIMESTAMP%.txt
    findstr /c:"TOTAL" cold_start_results\app_memory_1s_%TIMESTAMP%.txt >> cold_start_results\performance_analysis_%TIMESTAMP%.txt
    echo Memory at 3 seconds: >> cold_start_results\performance_analysis_%TIMESTAMP%.txt
    findstr /c:"TOTAL" cold_start_results\app_memory_3s_%TIMESTAMP%.txt >> cold_start_results\performance_analysis_%TIMESTAMP%.txt
    echo Memory at 5 seconds: >> cold_start_results\performance_analysis_%TIMESTAMP%.txt
    findstr /c:"TOTAL" cold_start_results\app_memory_5s_%TIMESTAMP%.txt >> cold_start_results\performance_analysis_%TIMESTAMP%.txt
    echo. >> cold_start_results\performance_analysis_%TIMESTAMP%.txt
)

echo Checking for UI blocking issues...
if exist cold_start_results\ui_blocking_%TIMESTAMP%.log (
    echo UI Blocking Analysis: >> cold_start_results\performance_analysis_%TIMESTAMP%.txt
    findstr /c:"ANR" cold_start_results\ui_blocking_%TIMESTAMP%.log >> cold_start_results\performance_analysis_%TIMESTAMP%.txt
    findstr /c:"Skipped" cold_start_results\ui_blocking_%TIMESTAMP%.log >> cold_start_results\performance_analysis_%TIMESTAMP%.txt
    if errorlevel 1 (
        echo No UI blocking issues detected >> cold_start_results\performance_analysis_%TIMESTAMP%.txt
    )
    echo. >> cold_start_results\performance_analysis_%TIMESTAMP%.txt
)

echo ========================================
echo Cold Start Performance Test Results
echo ========================================

echo Test completed at %date% %time%
echo Results saved in cold_start_results directory:
echo - cold_start_times_%TIMESTAMP%.csv
echo - cold_start_detailed_%TIMESTAMP%.txt
echo - splash_timing_%TIMESTAMP%.log
echo - app_memory_*_%TIMESTAMP%.txt
echo - background_activity_%TIMESTAMP%.log
echo - ui_blocking_%TIMESTAMP%.log
echo - performance_analysis_%TIMESTAMP%.txt

echo.
echo Performance Targets:
echo - Total cold start time: <3000ms
echo - Splash screen display: <500ms
echo - Memory usage: Stable, no leaks
echo - No ANR or UI blocking

if exist cold_start_results\performance_analysis_%TIMESTAMP%.txt (
    echo.
    echo Performance Analysis Summary:
    type cold_start_results\performance_analysis_%TIMESTAMP%.txt
)

pause
