@echo off
REM Performance validation script for TJ_BatteryOne Enhanced Splash Activity
REM Tests cold start performance and initialization timing

echo ========================================
echo TJ_BatteryOne Performance Validation
echo ========================================

set ADB_PATH=E:\IDE\Android\SDK\platform-tools\adb.exe
set APP_ID=com.fc.p.tj.charginganimation.batterycharging.chargeeffect

echo.
echo === Test 1: Cold Start Performance ===
echo Target: Less than 3 seconds total startup time

echo Stopping app...
%ADB_PATH% shell am force-stop %APP_ID%

echo Clearing app cache...
%ADB_PATH% shell pm clear %APP_ID%

echo Starting cold start measurement...
%ADB_PATH% shell am start -W -n %APP_ID%/.activity.splash.SplashActivity > cold_start_result.txt

echo Cold start results:
type cold_start_result.txt

echo.
echo === Test 2: Splash Screen Timing ===
echo Target: Maximum 5 seconds splash duration

echo Restarting app for splash timing test...
%ADB_PATH% shell am force-stop %APP_ID%
timeout /t 2 /nobreak > nul

echo Starting app and monitoring splash progress...
start /b %ADB_PATH% logcat -s SPLASH_PROGRESS:D STARTUP_TIMING:D > splash_timing.log

%ADB_PATH% shell am start -n %APP_ID%/.activity.splash.SplashActivity

echo Waiting for splash completion...
timeout /t 8 /nobreak > nul

echo Stopping logcat...
taskkill /f /im adb.exe > nul 2>&1

echo.
echo === Test 3: MAX SDK Initialization ===
echo Target: Background initialization without blocking UI

echo Restarting for MAX SDK test...
%ADB_PATH% shell am force-stop %APP_ID%
timeout /t 2 /nobreak > nul

echo Monitoring MAX SDK initialization...
start /b %ADB_PATH% logcat -s MAX_INIT:D AD_ADAPTER_LOAD:D > max_sdk_init.log

%ADB_PATH% shell am start -n %APP_ID%/.activity.splash.SplashActivity

echo Waiting for MAX SDK initialization...
timeout /t 10 /nobreak > nul

echo Stopping logcat...
taskkill /f /im adb.exe > nul 2>&1

echo.
echo === Test 4: Service Initialization ===
echo Target: Battery services ready within 2 seconds

echo Restarting for service test...
%ADB_PATH% shell am force-stop %APP_ID%
timeout /t 2 /nobreak > nul

echo Monitoring service initialization...
start /b %ADB_PATH% logcat -s ServiceInitHelper:D CoreBatteryStatsService:D > service_init.log

%ADB_PATH% shell am start -n %APP_ID%/.activity.splash.SplashActivity

echo Waiting for service initialization...
timeout /t 5 /nobreak > nul

echo Stopping logcat...
taskkill /f /im adb.exe > nul 2>&1

echo.
echo ========================================
echo Test Results Summary
echo ========================================

echo.
echo Cold Start Results:
if exist cold_start_result.txt (
    findstr "TotalTime" cold_start_result.txt
) else (
    echo Cold start result file not found
)

echo.
echo Splash Timing Results:
if exist splash_timing.log (
    echo Checking splash progress logs...
    findstr "SPLASH_PROGRESS" splash_timing.log | findstr "100%"
    findstr "STARTUP_TIMING.*Total initialization time" splash_timing.log
) else (
    echo Splash timing log not found
)

echo.
echo MAX SDK Initialization Results:
if exist max_sdk_init.log (
    echo Checking MAX SDK initialization...
    findstr "MAX_INIT.*initialization completed" max_sdk_init.log
    findstr "AD_ADAPTER_LOAD.*completed successfully" max_sdk_init.log
) else (
    echo MAX SDK log not found
)

echo.
echo Service Initialization Results:
if exist service_init.log (
    echo Checking service initialization...
    findstr "ServiceInitHelper.*ready" service_init.log
    findstr "CoreBatteryStatsService.*started" service_init.log
) else (
    echo Service log not found
)

echo.
echo ========================================
echo Performance Benchmarks Check
echo ========================================

echo.
echo Expected Performance Targets:
echo - Cold start: Less than 3000ms
echo - Splash duration: Maximum 5000ms
echo - Fragment switching: Less than 500ms
echo - Data flow latency: Less than 100ms
echo - UI updates: Less than 50ms

echo.
echo Test completed. Check log files for detailed analysis:
echo - cold_start_result.txt
echo - splash_timing.log
echo - max_sdk_init.log
echo - service_init.log

pause
