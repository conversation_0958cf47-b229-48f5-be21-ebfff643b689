package com.tqhit.battery.one.initialization;

import android.content.Context;
import com.tqhit.battery.one.features.stats.corebattery.service.CoreBatteryServiceHelper;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class ServiceInitializationHelper_Factory implements Factory<ServiceInitializationHelper> {
  private final Provider<Context> contextProvider;

  private final Provider<CoreBatteryServiceHelper> coreBatteryServiceHelperProvider;

  public ServiceInitializationHelper_Factory(Provider<Context> contextProvider,
      Provider<CoreBatteryServiceHelper> coreBatteryServiceHelperProvider) {
    this.contextProvider = contextProvider;
    this.coreBatteryServiceHelperProvider = coreBatteryServiceHelperProvider;
  }

  @Override
  public ServiceInitializationHelper get() {
    return newInstance(contextProvider.get(), coreBatteryServiceHelperProvider.get());
  }

  public static ServiceInitializationHelper_Factory create(Provider<Context> contextProvider,
      Provider<CoreBatteryServiceHelper> coreBatteryServiceHelperProvider) {
    return new ServiceInitializationHelper_Factory(contextProvider, coreBatteryServiceHelperProvider);
  }

  public static ServiceInitializationHelper newInstance(Context context,
      CoreBatteryServiceHelper coreBatteryServiceHelper) {
    return new ServiceInitializationHelper(context, coreBatteryServiceHelper);
  }
}
