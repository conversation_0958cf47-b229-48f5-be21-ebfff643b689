@echo off
REM Comprehensive Splash Activity Validation Framework
REM TJ_BatteryOne Enhanced Splash with Asynchronous MAX SDK Initialization
REM Application ID: com.fc.p.tj.charginganimation.batterycharging.chargeeffect

setlocal enabledelayedexpansion

echo ==========================================
echo TJ_BatteryOne Splash Validation Framework
echo ==========================================

set ADB_PATH=E:\IDE\Android\SDK\platform-tools\adb.exe
set APP_ID=com.fc.p.tj.charginganimation.batterycharging.chargeeffect
set TEST_RESULTS_DIR=test_results
set TIMESTAMP=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%

REM Create results directory
if not exist %TEST_RESULTS_DIR% mkdir %TEST_RESULTS_DIR%

echo Test Session: %TIMESTAMP%
echo Application ID: %APP_ID%
echo ADB Path: %ADB_PATH%
echo.

REM Check ADB connection
echo === Checking ADB Connection ===
%ADB_PATH% devices > %TEST_RESULTS_DIR%\adb_devices_%TIMESTAMP%.txt
type %TEST_RESULTS_DIR%\adb_devices_%TIMESTAMP%.txt

findstr "emulator-5554" %TEST_RESULTS_DIR%\adb_devices_%TIMESTAMP%.txt >nul
if errorlevel 1 (
    echo ERROR: emulator-5554 not found. Please start the emulator.
    pause
    exit /b 1
)

echo Device connected successfully.
echo.

REM Test 1: UI Responsiveness Testing
echo === Test 1: UI Responsiveness Testing ===
echo Target: Progress bar updates <50ms, smooth animations, no UI blocking

call :run_ui_responsiveness_test

REM Test 2: Cold Start Performance Analysis
echo === Test 2: Cold Start Performance Analysis ===
echo Target: Total startup <3 seconds, splash display <500ms

call :run_cold_start_test

REM Test 3: Initialization Timing Validation
echo === Test 3: Initialization Timing Validation ===
echo Target: 5-second max timeout, early completion, smooth transitions

call :run_initialization_timing_test

REM Test 4: MAX SDK Integration Testing
echo === Test 4: MAX SDK Integration Testing ===
echo Target: Background initialization, no UI blocking, fallback behavior

call :run_max_sdk_integration_test

REM Test 5: Ad Display Continuity Testing
echo === Test 5: Ad Display Continuity Testing ===
echo Target: All ad types work after splash completion

call :run_ad_continuity_test

REM Test 6: Error Recovery Testing
echo === Test 6: Error Recovery Testing ===
echo Target: Graceful handling of initialization failures

call :run_error_recovery_test

REM Generate comprehensive report
call :generate_test_report

echo.
echo ==========================================
echo All tests completed. Check results in %TEST_RESULTS_DIR%
echo ==========================================
pause
exit /b 0

REM ==========================================
REM Test Functions
REM ==========================================

:run_ui_responsiveness_test
echo Running UI Responsiveness Test...

REM Clear app and start fresh
%ADB_PATH% shell am force-stop %APP_ID%
%ADB_PATH% shell pm clear %APP_ID%
timeout /t 2 /nobreak >nul

REM Start monitoring UI performance
start /b %ADB_PATH% logcat -s SPLASH_PROGRESS:D STARTUP_TIMING:D InitProgressManager:D SplashActivity:D > %TEST_RESULTS_DIR%\ui_responsiveness_%TIMESTAMP%.log

REM Launch app
%ADB_PATH% shell am start -n %APP_ID%/.activity.splash.SplashActivity

REM Monitor for 10 seconds
echo Monitoring UI responsiveness for 10 seconds...
timeout /t 10 /nobreak >nul

REM Stop monitoring
taskkill /f /im adb.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo UI Responsiveness Test completed.
echo.
goto :eof

:run_cold_start_test
echo Running Cold Start Performance Test...

REM Multiple cold start measurements
for /l %%i in (1,1,3) do (
    echo Cold start test run %%i of 3...
    
    %ADB_PATH% shell am force-stop %APP_ID%
    timeout /t 3 /nobreak >nul
    
    REM Measure cold start time
    %ADB_PATH% shell am start -W -n %APP_ID%/.activity.splash.SplashActivity >> %TEST_RESULTS_DIR%\cold_start_%TIMESTAMP%.txt
    
    timeout /t 5 /nobreak >nul
)

echo Cold Start Performance Test completed.
echo.
goto :eof

:run_initialization_timing_test
echo Running Initialization Timing Test...

REM Test normal initialization
%ADB_PATH% shell am force-stop %APP_ID%
%ADB_PATH% shell pm clear %APP_ID%
timeout /t 2 /nobreak >nul

start /b %ADB_PATH% logcat -s SPLASH_PROGRESS:D STARTUP_TIMING:D > %TEST_RESULTS_DIR%\init_timing_%TIMESTAMP%.log

%ADB_PATH% shell am start -n %APP_ID%/.activity.splash.SplashActivity

REM Monitor for full timeout period
echo Monitoring initialization timing for 8 seconds...
timeout /t 8 /nobreak >nul

taskkill /f /im adb.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo Initialization Timing Test completed.
echo.
goto :eof

:run_max_sdk_integration_test
echo Running MAX SDK Integration Test...

%ADB_PATH% shell am force-stop %APP_ID%
%ADB_PATH% shell pm clear %APP_ID%
timeout /t 2 /nobreak >nul

start /b %ADB_PATH% logcat -s MAX_INIT:D AD_ADAPTER_LOAD:D SPLASH_PROGRESS:D > %TEST_RESULTS_DIR%\max_sdk_%TIMESTAMP%.log

%ADB_PATH% shell am start -n %APP_ID%/.activity.splash.SplashActivity

echo Monitoring MAX SDK initialization for 15 seconds...
timeout /t 15 /nobreak >nul

taskkill /f /im adb.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo MAX SDK Integration Test completed.
echo.
goto :eof

:run_ad_continuity_test
echo Running Ad Display Continuity Test...

%ADB_PATH% shell am force-stop %APP_ID%
timeout /t 2 /nobreak >nul

start /b %ADB_PATH% logcat -s MAX_INIT:D AD_ADAPTER_LOAD:D MainActivity:D > %TEST_RESULTS_DIR%\ad_continuity_%TIMESTAMP%.log

%ADB_PATH% shell am start -n %APP_ID%/.activity.splash.SplashActivity

echo Waiting for app to reach MainActivity and monitoring ad loading...
timeout /t 20 /nobreak >nul

taskkill /f /im adb.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo Ad Display Continuity Test completed.
echo.
goto :eof

:run_error_recovery_test
echo Running Error Recovery Test...

REM Test with network disabled to simulate MAX SDK failure
echo Disabling network to test error recovery...
%ADB_PATH% shell svc wifi disable
%ADB_PATH% shell svc data disable

%ADB_PATH% shell am force-stop %APP_ID%
%ADB_PATH% shell pm clear %APP_ID%
timeout /t 2 /nobreak >nul

start /b %ADB_PATH% logcat -s SPLASH_PROGRESS:D MAX_INIT:D InitProgressManager:D > %TEST_RESULTS_DIR%\error_recovery_%TIMESTAMP%.log

%ADB_PATH% shell am start -n %APP_ID%/.activity.splash.SplashActivity

echo Monitoring error recovery for 10 seconds...
timeout /t 10 /nobreak >nul

REM Re-enable network
echo Re-enabling network...
%ADB_PATH% shell svc wifi enable
%ADB_PATH% shell svc data enable

taskkill /f /im adb.exe >nul 2>&1
timeout /t 2 /nobreak >nul

echo Error Recovery Test completed.
echo.
goto :eof

:generate_test_report
echo Generating comprehensive test report...

echo ========================================== > %TEST_RESULTS_DIR%\test_report_%TIMESTAMP%.txt
echo TJ_BatteryOne Splash Validation Report >> %TEST_RESULTS_DIR%\test_report_%TIMESTAMP%.txt
echo Test Session: %TIMESTAMP% >> %TEST_RESULTS_DIR%\test_report_%TIMESTAMP%.txt
echo ========================================== >> %TEST_RESULTS_DIR%\test_report_%TIMESTAMP%.txt
echo. >> %TEST_RESULTS_DIR%\test_report_%TIMESTAMP%.txt

echo === Cold Start Performance Results === >> %TEST_RESULTS_DIR%\test_report_%TIMESTAMP%.txt
if exist %TEST_RESULTS_DIR%\cold_start_%TIMESTAMP%.txt (
    findstr "TotalTime" %TEST_RESULTS_DIR%\cold_start_%TIMESTAMP%.txt >> %TEST_RESULTS_DIR%\test_report_%TIMESTAMP%.txt
) else (
    echo Cold start results not available >> %TEST_RESULTS_DIR%\test_report_%TIMESTAMP%.txt
)
echo. >> %TEST_RESULTS_DIR%\test_report_%TIMESTAMP%.txt

echo === UI Responsiveness Analysis === >> %TEST_RESULTS_DIR%\test_report_%TIMESTAMP%.txt
if exist %TEST_RESULTS_DIR%\ui_responsiveness_%TIMESTAMP%.log (
    findstr "SPLASH_PROGRESS.*UI updated" %TEST_RESULTS_DIR%\ui_responsiveness_%TIMESTAMP%.log >> %TEST_RESULTS_DIR%\test_report_%TIMESTAMP%.txt
) else (
    echo UI responsiveness results not available >> %TEST_RESULTS_DIR%\test_report_%TIMESTAMP%.txt
)
echo. >> %TEST_RESULTS_DIR%\test_report_%TIMESTAMP%.txt

echo === Initialization Timing Results === >> %TEST_RESULTS_DIR%\test_report_%TIMESTAMP%.txt
if exist %TEST_RESULTS_DIR%\init_timing_%TIMESTAMP%.log (
    findstr "Total initialization time" %TEST_RESULTS_DIR%\init_timing_%TIMESTAMP%.log >> %TEST_RESULTS_DIR%\test_report_%TIMESTAMP%.txt
    findstr "100%" %TEST_RESULTS_DIR%\init_timing_%TIMESTAMP%.log >> %TEST_RESULTS_DIR%\test_report_%TIMESTAMP%.txt
) else (
    echo Initialization timing results not available >> %TEST_RESULTS_DIR%\test_report_%TIMESTAMP%.txt
)
echo. >> %TEST_RESULTS_DIR%\test_report_%TIMESTAMP%.txt

echo === MAX SDK Integration Results === >> %TEST_RESULTS_DIR%\test_report_%TIMESTAMP%.txt
if exist %TEST_RESULTS_DIR%\max_sdk_%TIMESTAMP%.log (
    findstr "MAX_INIT.*initialization completed" %TEST_RESULTS_DIR%\max_sdk_%TIMESTAMP%.log >> %TEST_RESULTS_DIR%\test_report_%TIMESTAMP%.txt
    findstr "AD_ADAPTER_LOAD.*completed successfully" %TEST_RESULTS_DIR%\max_sdk_%TIMESTAMP%.log >> %TEST_RESULTS_DIR%\test_report_%TIMESTAMP%.txt
) else (
    echo MAX SDK integration results not available >> %TEST_RESULTS_DIR%\test_report_%TIMESTAMP%.txt
)
echo. >> %TEST_RESULTS_DIR%\test_report_%TIMESTAMP%.txt

echo Test report generated: %TEST_RESULTS_DIR%\test_report_%TIMESTAMP%.txt
goto :eof
