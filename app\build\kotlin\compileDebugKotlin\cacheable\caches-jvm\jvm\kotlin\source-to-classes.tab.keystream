   = a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / B a t t e r y A p p l i c a t i o n . k t   > a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / u t i l s / B a t t e r y L o g g e r . k t   O a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / a n i m a t i o n / A n i m a t i o n A c t i v i t y . k t   G a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / d e b u g / D e b u g A c t i v i t y . k t   E a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / m a i n / M a i n A c t i v i t y . k t   Z a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / m a i n / h a n d l e r s / F r a g m e n t L i f e c y c l e M a n a g e r . k t   S a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / m a i n / h a n d l e r s / N a v i g a t i o n H a n d l e r . k t   P a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / m a i n / h a n d l e r s / S e r v i c e M a n a g e r . k t   S a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / o v e r l a y / C h a r g i n g O v e r l a y A c t i v i t y . k t   R a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / p a s s w o r d / E n t e r P a s s w o r d A c t i v i t y . k t   I a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / s p l a s h / S p l a s h A c t i v i t y . k t   M a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / s t a r t i n g / S t a r t i n g A c t i v i t y . k t   P a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / a c t i v i t y / s t a r t i n g / S t a r t i n g V i e w A d a p t e r . k t   L a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / a d s / c o r e / A p p l o v i n A p p O p e n A d M a n a g e r . k t   K a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / a d s / c o r e / A p p l o v i n B a n n e r A d M a n a g e r . k t   Q a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / a d s / c o r e / A p p l o v i n I n t e r s t i t i a l A d M a n a g e r . k t   K a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / a d s / c o r e / A p p l o v i n N a t i v e A d M a n a g e r . k t   M a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / a d s / c o r e / A p p l o v i n R e w a r d e d A d M a n a g e r . k t   Q a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / c o m p o n e n t / p r o g r e s s / V e r t i c a l P r o g r e s s B a r . k t   G a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / d i / T h u m b n a i l P r e l o a d i n g M o d u l e . k t   P a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / d i a l o g / a l a r m / S e l e c t B a t t e r y A l a r m D i a l o g . k t   S a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / d i a l o g / a l a r m / S e l e c t B a t t e r y A l a r m L o w D i a l o g . k t   O a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / d i a l o g / c a p a c i t y / C h a n g e C a p a c i t y D i a l o g . k t   N a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / d i a l o g / c a p a c i t y / S e t u p P a s s w o r d D i a l o g . k t   O a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / d i a l o g / l a n g u a g e / S e l e c t L a n g u a g e D i a l o g . k t   W a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / d i a l o g / p e r m i s s i o n / B a c k g r o u n d P e r m i s s i o n D i a l o g . k t   I a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / d i a l o g / t h e m e / S e l e c t C o l o r D i a l o g . k t   I a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / d i a l o g / t h e m e / S e l e c t T h e m e D i a l o g . k t   K a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / d i a l o g / u t i l s / I n t e r f a c e A p p l o v i n A d . k t   E a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / d i a l o g / u t i l s / L o a d i n g D i a l o g . k t   J a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / d i a l o g / u t i l s / N o t i f i c a t i o n D i a l o g . k t   K a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / d i a l o g / u t i l s / P o s t A n i m a t i o n D i a l o g . k t   K a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / n a v i g a t i o n / A p p N a v i g a t o r . k t   W a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / n a v i g a t i o n / D y n a m i c N a v i g a t i o n M a n a g e r . k t   N a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / n a v i g a t i o n / N a v i g a t i o n S t a t e . k t   X a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / n a v i g a t i o n / S h a r e d N a v i g a t i o n V i e w M o d e l . k t   T a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / n a v i g a t i o n / d i / N a v i g a t i o n D I M o d u l e . k t   _ a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / a p p p o w e r / d a t a / A p p P o w e r C o n s u m p t i o n D a t a . k t   i a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / a p p p o w e r / p e r m i s s i o n / U s a g e S t a t s P e r m i s s i o n M a n a g e r . k t   j a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / a p p p o w e r / p r e s e n t a t i o n / A p p P o w e r C o n s u m p t i o n A d a p t e r . k t   i a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / a p p p o w e r / p r e s e n t a t i o n / A p p P o w e r C o n s u m p t i o n D i a l o g . k t   p a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / a p p p o w e r / p r e s e n t a t i o n / A p p P o w e r C o n s u m p t i o n D i a l o g F a c t o r y . k t   e a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / a p p p o w e r / r e p o s i t o r y / A p p U s a g e S t a t s R e p o s i t o r y . k t   \ a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / c a c h e / S t a t s C h a r g e P r e f s C a c h e . k t   X a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / d a t a / S t a t s C h a r g e S e s s i o n . k t   W a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / d a t a / S t a t s C h a r g e S t a t u s . k t   W a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / d i / S t a t s C h a r g e D I M o d u l e . k t   l a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / d o m a i n / C a l c u l a t e S i m p l e C h a r g e E s t i m a t e U s e C a s e . k t   a a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / p r e s e n t a t i o n / S t a t s C h a r g e F r a g m e n t . k t   b a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / p r e s e n t a t i o n / S t a t s C h a r g e V i e w M o d e l . k t   a a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / r e p o s i t o r y / S t a t s C h a r g e R e p o s i t o r y . k t   \ a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c o r e b a t t e r y / d a t a / C o r e B a t t e r y S t a t u s . k t   \ a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c o r e b a t t e r y / d i / C o r e B a t t e r y D I M o d u l e . k t   e a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c o r e b a t t e r y / d o m a i n / C o r e B a t t e r y S t a t s P r o v i d e r . k t   f a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c o r e b a t t e r y / s e r v i c e / C o r e B a t t e r y S e r v i c e H e l p e r . k t   e a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c o r e b a t t e r y / s e r v i c e / C o r e B a t t e r y S t a t s S e r v i c e . k t   ] a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / c a c h e / C u r r e n t S e s s i o n C a c h e . k t   ] a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / c a c h e / D i s c h a r g e R a t e s C a c h e . k t   b a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / c a c h e / P r e f s C u r r e n t S e s s i o n C a c h e . k t   b a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / c a c h e / P r e f s D i s c h a r g e R a t e s C a c h e . k t   ] a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d a t a / D i s c h a r g e S e s s i o n D a t a . k t   _ a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d a t a / S c r e e n S t a t e C h a n g e E v e n t . k t   b a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d a t a s o u r c e / S c r e e n S t a t e R e c e i v e r . k t   [ a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d i / S t a t s D i s c h a r g e M o d u l e . k t   ^ a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / A p p L i f e c y c l e M a n a g e r . k t   ^ a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / D i s c h a r g e C a l c u l a t o r . k t   b a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / D i s c h a r g e R a t e C a l c u l a t o r . k t   a a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / F u l l S e s s i o n R e E s t i m a t o r . k t   b a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / G a p E s t i m a t i o n C a l c u l a t o r . k t   a a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / S c r e e n S t a t e T i m e T r a c k e r . k t   _ a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / S c r e e n T i m e C a l c u l a t o r . k t   f a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / S c r e e n T i m e V a l i d a t i o n S e r v i c e . k t   Y a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / S e s s i o n M a n a g e r . k t   c a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / S e s s i o n M e t r i c s C a l c u l a t o r . k t   X a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / d o m a i n / T i m e C o n v e r t e r . k t   ` a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / p r e s e n t a t i o n / A n i m a t i o n H e l p e r . k t   b a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / p r e s e n t a t i o n / D i s c h a r g e F r a g m e n t . k t   c a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / p r e s e n t a t i o n / D i s c h a r g e U i U p d a t e r . k t   c a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / p r e s e n t a t i o n / D i s c h a r g e V i e w M o d e l . k t   b a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / p r e s e n t a t i o n / I n f o B u t t o n M a n a g e r . k t   ` a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / r e p o s i t o r y / B a t t e r y R e p o s i t o r y . k t   i a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / r e p o s i t o r y / D i s c h a r g e S e s s i o n R e p o s i t o r y . k t   i a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / s e r v i c e / E n h a n c e d D i s c h a r g e T i m e r S e r v i c e . k t   o a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / d i s c h a r g e / s e r v i c e / E n h a n c e d D i s c h a r g e T i m e r S e r v i c e H e l p e r . k t   R a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / c a c h e / H e a l t h C a c h e . k t   U a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / d a t a / H e a l t h C h a r t D a t a . k t   R a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / d a t a / H e a l t h S t a t u s . k t   R a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / d i / H e a l t h D I M o d u l e . k t   e a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / d o m a i n / C a l c u l a t e B a t t e r y H e a l t h U s e C a s e . k t   _ a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / d o m a i n / G e t H e a l t h H i s t o r y U s e C a s e . k t   ] a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / p r e s e n t a t i o n / H e a l t h V i e w M o d e l . k t   \ a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / r e p o s i t o r y / H e a l t h R e p o s i t o r y . k t   d a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / h e a l t h / r e p o s i t o r y / H i s t o r y B a t t e r y R e p o s i t o r y . k t   i a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / n o t i f i c a t i o n s / U n i f i e d B a t t e r y N o t i f i c a t i o n S e r v i c e . k t   o a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / n o t i f i c a t i o n s / U n i f i e d B a t t e r y N o t i f i c a t i o n S e r v i c e H e l p e r . k t   G a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / H e a l t h F r a g m e n t . k t   I a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / S e t t i n g s F r a g m e n t . k t   X a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / A n i m a t i o n G r i d F r a g m e n t . k t   [ a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / a d a p t e r / A n i m a t i o n A d a p t e r . k t   Z a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / a d a p t e r / C a t e g o r y A d a p t e r . k t   Y a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / d a t a / A n i m a t i o n C a t e g o r y . k t   U a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / a n i m a t i o n / d a t a / A n i m a t i o n I t e m . k t   N a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / o t h e r s / O t h e r s F r a g m e n t . k t   U a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / o t h e r s / a d a p t e r / O t h e r s A d a p t e r . k t   S a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f r a g m e n t / m a i n / o t h e r s / d a t a / O t h e r s I t e m D a t a . k t   Q a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / a n i m a t i o n / A n i m a t i o n F i l e M a n a g e r . k t   G a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / c h a r g e / C h a r g e S e s s i o n . k t   P a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / c h a r g e / C h a r g i n g S e s s i o n M a n a g e r . k t   M a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / d i s c h a r g e / D i s c h a r g e S e s s i o n . k t   T a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / d i s c h a r g e / D i s c h a r g e S e s s i o n M a n a g e r . k t   G a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / g r a p h / H i s t o r y M a n a g e r . k t   E a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / t h e m e / T h e m e M a n a g e r . k t   Q a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / m a n a g e r / t h u m b n a i l / T h u m b n a i l F i l e M a n a g e r . k t   S a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / A n i m a t i o n P r e l o a d i n g R e p o s i t o r y . k t   I a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / A n i m a t i o n R e p o s i t o r y . k t   C a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / A p p R e p o s i t o r y . k t   G a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / B a t t e r y R e p o s i t o r y . k t   S a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / r e p o s i t o r y / T h u m b n a i l P r e l o a d i n g R e p o s i t o r y . k t   I a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / C h a r g i n g O v e r l a y S e r v i c e . k t   O a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / C h a r g i n g O v e r l a y S e r v i c e H e l p e r . k t   C a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / V i b r a t i o n S e r v i c e . k t   Q a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / a n i m a t i o n / A n i m a t i o n D a t a S e r v i c e . k t   O a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / a n i m a t i o n / A n i m a t i o n P r e l o a d e r . k t   _ a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / t h u m b n a i l / D e f e r r e d T h u m b n a i l P r e l o a d i n g S e r v i c e . k t   Q a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / t h u m b n a i l / T h u m b n a i l D a t a S e r v i c e . k t   O a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / s e r v i c e / t h u m b n a i l / T h u m b n a i l P r e l o a d e r . k t   ? a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / u t i l s / A n t i T h i e f U t i l s . k t   L a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / u t i l s / B a c k g r o u n d P e r m i s s i o n M a n a g e r . k t   K a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / u t i l s / B a t t e r y C a l c u l a t o r D i s c h a r g e . k t   = a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / u t i l s / B a t t e r y U t i l s . k t   > a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / u t i l s / D a t e T i m e U t i l s . k t   < a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / u t i l s / D e v i c e U t i l s . k t   G a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / u t i l s / F o r e g r o u n d S e r v i c e U t i l s . k t   B a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / u t i l s / N o t i f i c a t i o n U t i l s . k t   G a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / u t i l s / O v e r l a y P e r m i s s i o n U t i l s . k t   @ a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / u t i l s / P e r m i s s i o n U t i l s . k t   B a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / u t i l s / P r e l o a d i n g M o n i t o r . k t   ; a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / u t i l s / V i d e o U t i l s . k t   A a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / v i e w m o d e l / A p p V i e w M o d e l . k t   Q a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / v i e w m o d e l / a n i m a t i o n / A n i m a t i o n V i e w M o d e l . k t   M a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / v i e w m o d e l / b a t t e r y / B a t t e r y V i e w M o d e l . k t   h a p p / s r c / m a i n / j a v a / c o m / t q h i t / b a t t e r y / o n e / f e a t u r e s / s t a t s / c h a r g e / p r e s e n t a t i o n / S t a t s C h a r g e F r a g m e n t _ b a c k u p . k t                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              