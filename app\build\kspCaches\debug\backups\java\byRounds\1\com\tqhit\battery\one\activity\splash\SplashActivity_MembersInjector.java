package com.tqhit.battery.one.activity.splash;

import com.tqhit.battery.one.initialization.InitializationProgressManager;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.Provider;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava",
    "cast",
    "deprecation",
    "nullness:initialization.field.uninitialized"
})
public final class SplashActivity_MembersInjector implements MembersInjector<SplashActivity> {
  private final Provider<InitializationProgressManager> initializationProgressManagerProvider;

  public SplashActivity_MembersInjector(
      Provider<InitializationProgressManager> initializationProgressManagerProvider) {
    this.initializationProgressManagerProvider = initializationProgressManagerProvider;
  }

  public static MembersInjector<SplashActivity> create(
      Provider<InitializationProgressManager> initializationProgressManagerProvider) {
    return new SplashActivity_MembersInjector(initializationProgressManagerProvider);
  }

  @Override
  public void injectMembers(SplashActivity instance) {
    injectInitializationProgressManager(instance, initializationProgressManagerProvider.get());
  }

  @InjectedFieldSignature("com.tqhit.battery.one.activity.splash.SplashActivity.initializationProgressManager")
  public static void injectInitializationProgressManager(SplashActivity instance,
      InitializationProgressManager initializationProgressManager) {
    instance.initializationProgressManager = initializationProgressManager;
  }
}
