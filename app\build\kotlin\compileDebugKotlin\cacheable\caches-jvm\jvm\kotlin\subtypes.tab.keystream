(com.tqhit.adlib.sdk.AdLibHiltApplication$androidx.lifecycle.LifecycleObserver-com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity(androidx.appcompat.app.AppCompatActivity&androidx.viewpager.widget.PagerAdapter$com.applovin.mediation.MaxAdListener+com.applovin.mediation.MaxAdRevenueListener,com.applovin.mediation.MaxRewardedAdListenerandroid.widget.ProgressBar+com.tqhit.adlib.sdk.base.ui.AdLibBaseDialogkotlin.Enumandroidx.lifecycle.ViewModel(androidx.recyclerview.widget.ListAdapter4androidx.recyclerview.widget.RecyclerView.ViewHolder2androidx.recyclerview.widget.DiffUtil.ItemCallbackandroid.app.DialogBcom.tqhit.battery.one.features.stats.charge.cache.StatsChargeCache-com.tqhit.adlib.sdk.base.ui.AdLibBaseFragmentLcom.tqhit.battery.one.features.stats.charge.repository.StatsChargeRepositoryPcom.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProviderandroid.app.ServiceHcom.tqhit.battery.one.features.stats.discharge.cache.CurrentSessionCacheHcom.tqhit.battery.one.features.stats.discharge.cache.DischargeRatesCache+androidx.lifecycle.DefaultLifecycleObserverFcom.tqhit.battery.one.features.stats.discharge.domain.ValidationResultandroidx.fragment.app.Fragment=<EMAIL>:com.tqhit.battery.one.repository.ThumbnailPreloadingStatus:com.tqhit.battery.one.repository.ThumbnailPreloadingResult                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    