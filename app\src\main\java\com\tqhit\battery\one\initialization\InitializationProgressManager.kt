package com.tqhit.battery.one.initialization

import android.content.Context
import android.util.Log
import com.tqhit.battery.one.BatteryApplication
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Centralized manager for tracking and coordinating all app initialization steps.
 * Provides progress callbacks and status updates for splash screen UI.
 * 
 * Following clean architecture principles:
 * - Single Responsibility: Only handles initialization coordination
 * - Open/Closed: Extensible for new initialization steps
 * - Dependency Inversion: Depends on abstractions
 */
@Singleton
class InitializationProgressManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val serviceInitializationHelper: ServiceInitializationHelper
) {
    
    companion object {
        private const val TAG = "InitProgressManager"
        
        // Progress milestones for each initialization step
        private const val PROGRESS_BATTERY_SERVICES = 25
        private const val PROGRESS_MAX_SDK = 50
        private const val PROGRESS_AD_ADAPTERS = 75
        private const val PROGRESS_FINALIZATION = 100
    }
    
    private val initializationScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    
    // State flows for progress tracking
    private val _initializationProgress = MutableStateFlow(InitializationState())
    val initializationProgress: StateFlow<InitializationState> = _initializationProgress.asStateFlow()
    
    // Individual step completion tracking
    private var batteryServicesCompleted = false
    private var maxSdkCompleted = false
    private var adAdaptersCompleted = false
    private var finalizationCompleted = false
    
    /**
     * Start the initialization process with progress tracking
     */
    fun startInitialization() {
        Log.d(TAG, "SPLASH_PROGRESS: Starting initialization process")
        
        initializationScope.launch {
            try {
                updateProgress(0, "Starting initialization...")
                
                // Step 1: Initialize battery services
                initializeBatteryServices()
                
                // Step 2: Initialize MAX SDK
                initializeMaxSdk()
                
                // Step 3: Initialize ad adapters
                initializeAdAdapters()
                
                // Step 4: Finalize setup
                finalizeInitialization()
                
            } catch (e: Exception) {
                Log.e(TAG, "SPLASH_PROGRESS: Error during initialization", e)
                updateProgress(
                    _initializationProgress.value.progress,
                    "Initialization error: ${e.message}",
                    hasError = true
                )
            }
        }
    }
    
    /**
     * Initialize battery services asynchronously
     */
    private suspend fun initializeBatteryServices() {
        Log.d(TAG, "SPLASH_PROGRESS: Initializing battery services...")
        updateProgress(10, "Initializing battery services...")

        try {
            // Use ServiceInitializationHelper for proper service coordination
            val servicesReady = serviceInitializationHelper.initializeBatteryServices()

            if (servicesReady) {
                batteryServicesCompleted = true
                updateProgress(PROGRESS_BATTERY_SERVICES, "Battery services ready")
                Log.d(TAG, "SPLASH_PROGRESS: Battery services initialization completed successfully")
            } else {
                // Services not fully ready but continue anyway
                batteryServicesCompleted = true
                updateProgress(PROGRESS_BATTERY_SERVICES, "Battery services starting...")
                Log.w(TAG, "SPLASH_PROGRESS: Battery services not fully ready, continuing")
            }

        } catch (e: Exception) {
            Log.e(TAG, "SPLASH_PROGRESS: Error initializing battery services", e)
            updateProgress(PROGRESS_BATTERY_SERVICES, "Battery services error", hasError = true)
        }
    }
    
    /**
     * Initialize MAX SDK asynchronously
     */
    private suspend fun initializeMaxSdk() {
        Log.d(TAG, "SPLASH_PROGRESS: Loading MAX SDK...")
        updateProgress(30, "Loading MAX SDK...")

        try {
            // Get BatteryApplication instance for MAX SDK initialization
            val batteryApp = context.applicationContext as? BatteryApplication
            if (batteryApp != null) {
                Log.d(TAG, "MAX_INIT: Triggering MAX SDK initialization from splash")

                // Trigger MAX SDK initialization if not already done
                batteryApp.initializeMaxSdkWhenReady()

                // Wait for MAX SDK to initialize with timeout
                var waitTime = 0L
                val maxWaitTime = 2000L // 2 seconds max wait
                val checkInterval = 100L

                while (!batteryApp.isMaxSdkInitialized && waitTime < maxWaitTime) {
                    kotlinx.coroutines.delay(checkInterval)
                    waitTime += checkInterval

                    // Update progress during wait
                    val progressIncrement = (waitTime.toFloat() / maxWaitTime * 20).toInt()
                    updateProgress(30 + progressIncrement, "Loading MAX SDK...")
                }

                if (batteryApp.isMaxSdkInitialized) {
                    Log.d(TAG, "MAX_INIT: MAX SDK initialization confirmed")
                } else {
                    Log.w(TAG, "MAX_INIT: MAX SDK initialization timeout, continuing anyway")
                }
            } else {
                Log.w(TAG, "SPLASH_PROGRESS: Could not get BatteryApplication instance for MAX SDK")
                kotlinx.coroutines.delay(800) // Fallback delay
            }

            maxSdkCompleted = true
            updateProgress(PROGRESS_MAX_SDK, "MAX SDK ready")
            Log.d(TAG, "SPLASH_PROGRESS: MAX SDK initialization completed")

        } catch (e: Exception) {
            Log.e(TAG, "SPLASH_PROGRESS: Error initializing MAX SDK", e)
            Log.e(TAG, "MAX_INIT: MAX SDK initialization failed, continuing with fallback", e)

            // Continue anyway - app should remain functional without ads
            maxSdkCompleted = true
            updateProgress(PROGRESS_MAX_SDK, "MAX SDK fallback mode")
        }
    }
    
    /**
     * Initialize ad adapters asynchronously
     */
    private suspend fun initializeAdAdapters() {
        Log.d(TAG, "SPLASH_PROGRESS: Preparing ad adapters...")
        Log.d(TAG, "AD_ADAPTER_LOAD: Starting ad adapter initialization")
        updateProgress(60, "Preparing ad adapters...")

        try {
            val adapterStartTime = System.currentTimeMillis()

            // Get BatteryApplication instance for ad adapter access
            val batteryApp = context.applicationContext as? BatteryApplication
            if (batteryApp != null) {
                Log.d(TAG, "AD_ADAPTER_LOAD: BatteryApplication instance obtained")

                // Ad adapters are initialized as part of MAX SDK initialization
                // We just need to wait for them to be ready
                var waitTime = 0L
                val maxWaitTime = 1000L // 1 second max wait for ad adapters
                val checkInterval = 100L

                while (waitTime < maxWaitTime) {
                    // Check if MAX SDK is initialized (which includes ad adapters)
                    if (batteryApp.isMaxSdkInitialized) {
                        Log.d(TAG, "AD_ADAPTER_LOAD: Ad adapters ready via MAX SDK")
                        break
                    }

                    kotlinx.coroutines.delay(checkInterval)
                    waitTime += checkInterval

                    // Update progress during wait
                    val progressIncrement = (waitTime.toFloat() / maxWaitTime * 15).toInt()
                    updateProgress(60 + progressIncrement, "Preparing ad adapters...")
                }

                Log.d(TAG, "AD_ADAPTER_LOAD: Ad adapter initialization took ${System.currentTimeMillis() - adapterStartTime}ms")
            } else {
                Log.w(TAG, "AD_ADAPTER_LOAD: Could not get BatteryApplication instance")
                kotlinx.coroutines.delay(600) // Fallback delay
            }

            adAdaptersCompleted = true
            updateProgress(PROGRESS_AD_ADAPTERS, "Ad adapters ready")
            Log.d(TAG, "SPLASH_PROGRESS: Ad adapters initialization completed")
            Log.d(TAG, "AD_ADAPTER_LOAD: Ad adapter initialization completed successfully")

        } catch (e: Exception) {
            Log.e(TAG, "SPLASH_PROGRESS: Error initializing ad adapters", e)
            Log.e(TAG, "AD_ADAPTER_LOAD: Error during ad adapter initialization", e)

            // Continue anyway - app should remain functional without ads
            adAdaptersCompleted = true
            updateProgress(PROGRESS_AD_ADAPTERS, "Ad adapters fallback mode")
        }
    }
    
    /**
     * Finalize initialization process
     */
    private suspend fun finalizeInitialization() {
        Log.d(TAG, "SPLASH_PROGRESS: Finalizing setup...")
        updateProgress(90, "Finalizing setup...")
        
        try {
            // Final setup steps
            kotlinx.coroutines.delay(200) // Simulate finalization time
            
            finalizationCompleted = true
            updateProgress(PROGRESS_FINALIZATION, "Ready to launch", isComplete = true)
            Log.d(TAG, "SPLASH_PROGRESS: Initialization completed successfully")
            
        } catch (e: Exception) {
            Log.e(TAG, "SPLASH_PROGRESS: Error during finalization", e)
            updateProgress(PROGRESS_FINALIZATION, "Finalization error", hasError = true)
        }
    }
    
    /**
     * Update progress state and notify observers
     */
    private fun updateProgress(
        progress: Int,
        statusMessage: String,
        hasError: Boolean = false,
        isComplete: Boolean = false
    ) {
        val newState = InitializationState(
            progress = progress,
            statusMessage = statusMessage,
            hasError = hasError,
            isComplete = isComplete,
            timestamp = System.currentTimeMillis()
        )
        
        _initializationProgress.value = newState
        Log.d(TAG, "SPLASH_PROGRESS: Progress updated - $progress% - $statusMessage")
    }
    
    /**
     * Check if initialization is complete
     */
    fun isInitializationComplete(): Boolean {
        return _initializationProgress.value.isComplete
    }
    
    /**
     * Check if initialization has errors
     */
    fun hasInitializationError(): Boolean {
        return _initializationProgress.value.hasError
    }
    
    /**
     * Get current initialization progress percentage
     */
    fun getCurrentProgress(): Int {
        return _initializationProgress.value.progress
    }
    
    /**
     * Reset initialization state for testing purposes
     */
    fun resetInitialization() {
        Log.d(TAG, "SPLASH_PROGRESS: Resetting initialization state")
        batteryServicesCompleted = false
        maxSdkCompleted = false
        adAdaptersCompleted = false
        finalizationCompleted = false
        _initializationProgress.value = InitializationState()
    }
}

/**
 * Data class representing the current initialization state
 */
data class InitializationState(
    val progress: Int = 0,
    val statusMessage: String = "Initializing...",
    val hasError: Boolean = false,
    val isComplete: Boolean = false,
    val timestamp: Long = System.currentTimeMillis()
)
