/ Header Record For PersistentHashMapValueStorageN (com.tqhit.adlib.sdk.AdLibHiltApplication$androidx.lifecycle.LifecycleObserver. -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity) (androidx.appcompat.app.AppCompatActivity. -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity. -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity. -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity. -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity. -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity' &androidx.viewpager.widget.PagerAdapterQ $com.applovin.mediation.MaxAdListener+com.applovin.mediation.MaxAdRevenueListener, +com.applovin.mediation.MaxAdRevenueListenerQ $com.applovin.mediation.MaxAdListener+com.applovin.mediation.MaxAdRevenueListener, +com.applovin.mediation.MaxAdRevenueListenerY ,com.applovin.mediation.MaxRewardedAdListener+com.applovin.mediation.MaxAdRevenueListener android.widget.ProgressBar, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog, +com.tqhit.adlib.sdk.base.ui.AdLibBaseDialog kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback android.app.DialogC Bcom.tqhit.battery.one.features.stats.charge.cache.StatsChargeCache. -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment androidx.lifecycle.ViewModelM Lcom.tqhit.battery.one.features.stats.charge.repository.StatsChargeRepositoryQ Pcom.tqhit.battery.one.features.stats.corebattery.domain.CoreBatteryStatsProvider android.app.ServiceI Hcom.tqhit.battery.one.features.stats.discharge.cache.CurrentSessionCacheI Hcom.tqhit.battery.one.features.stats.discharge.cache.DischargeRatesCache, +androidx.lifecycle.DefaultLifecycleObserver kotlin.EnumG Fcom.tqhit.battery.one.features.stats.discharge.domain.ValidationResultG Fcom.tqhit.battery.one.features.stats.discharge.domain.ValidationResultG Fcom.tqhit.battery.one.features.stats.discharge.domain.ValidationResult kotlin.Enum kotlin.Enum androidx.fragment.app.Fragment androidx.lifecycle.ViewModel android.app.Service> =com.tqhit.battery.one.features.stats.health.cache.HealthCache kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModelH Gcom.tqhit.battery.one.features.stats.health.repository.HealthRepository android.app.Service kotlin.Enum. -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment. -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment. -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder9 8androidx.recyclerview.widget.RecyclerView.ItemDecoration2 1androidx.recyclerview.widget.RecyclerView.Adapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder kotlin.Enum kotlin.EnumA @com.tqhit.battery.one.fragment.main.animation.data.PreloadResultA @com.tqhit.battery.one.fragment.main.animation.data.PreloadResultA @com.tqhit.battery.one.fragment.main.animation.data.PreloadResultJ Icom.tqhit.battery.one.fragment.main.animation.data.ThumbnailPreloadResultJ Icom.tqhit.battery.one.fragment.main.animation.data.ThumbnailPreloadResultJ Icom.tqhit.battery.one.fragment.main.animation.data.ThumbnailPreloadResult. -com.tqhit.adlib.sdk.base.ui.AdLibBaseFragment) (androidx.recyclerview.widget.ListAdapter5 4androidx.recyclerview.widget.RecyclerView.ViewHolder3 2androidx.recyclerview.widget.DiffUtil.ItemCallback3 2com.tqhit.battery.one.manager.graph.HistoryManager3 2com.tqhit.battery.one.manager.graph.HistoryManager2 1com.tqhit.battery.one.repository.PreloadingResult2 1com.tqhit.battery.one.repository.PreloadingResult2 1com.tqhit.battery.one.repository.PreloadingResult2 1com.tqhit.battery.one.repository.PreloadingResult2 1com.tqhit.battery.one.repository.PreloadingResult2 1com.tqhit.battery.one.repository.PreloadingResult; :com.tqhit.battery.one.repository.ThumbnailPreloadingStatus; :com.tqhit.battery.one.repository.ThumbnailPreloadingStatus; :com.tqhit.battery.one.repository.ThumbnailPreloadingStatus; :com.tqhit.battery.one.repository.ThumbnailPreloadingStatus; :com.tqhit.battery.one.repository.ThumbnailPreloadingResult; :com.tqhit.battery.one.repository.ThumbnailPreloadingResult; :com.tqhit.battery.one.repository.ThumbnailPreloadingResult; :com.tqhit.battery.one.repository.ThumbnailPreloadingResult; :com.tqhit.battery.one.repository.ThumbnailPreloadingResult android.app.Service androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel. -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity. -com.tqhit.adlib.sdk.base.ui.AdLibBaseActivity